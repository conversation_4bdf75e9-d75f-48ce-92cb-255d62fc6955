<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add constraints to application_states table
        Schema::table('application_states', function (Blueprint $table) {
            // Add check constraints for enum values
            $table->check('channel IN ("web", "whatsapp", "ussd", "mobile_app")', 'chk_application_states_channel');
            
            // Add check constraint for current_step length
            $table->check('LENGTH(current_step) <= 50', 'chk_application_states_current_step_length');
            
            // Add check constraint for session_id format (basic validation)
            $table->check('LENGTH(session_id) > 0', 'chk_application_states_session_id_not_empty');
            
            // Add check constraint for reference_code format if present
            $table->check('reference_code IS NULL OR (LENGTH(reference_code) = 6 AND reference_code REGEXP "^[A-Z0-9]{6}$")', 'chk_application_states_reference_code_format');
        });

        // Add foreign key constraint to state_transitions table
        Schema::table('state_transitions', function (Blueprint $table) {
            // The foreign key constraint should already exist from the original migration
            // But let's ensure it's properly set up with cascade options
            $table->dropForeign(['state_id']);
            $table->foreign('state_id')
                  ->references('id')
                  ->on('application_states')
                  ->onDelete('cascade')
                  ->onUpdate('cascade');
                  
            // Add check constraints
            $table->check('LENGTH(to_step) <= 50', 'chk_state_transitions_to_step_length');
            $table->check('from_step IS NULL OR LENGTH(from_step) <= 50', 'chk_state_transitions_from_step_length');
            $table->check('LENGTH(channel) <= 20', 'chk_state_transitions_channel_length');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('application_states', function (Blueprint $table) {
            $table->dropCheckConstraint('chk_application_states_channel');
            $table->dropCheckConstraint('chk_application_states_current_step_length');
            $table->dropCheckConstraint('chk_application_states_session_id_not_empty');
            $table->dropCheckConstraint('chk_application_states_reference_code_format');
        });

        Schema::table('state_transitions', function (Blueprint $table) {
            $table->dropCheckConstraint('chk_state_transitions_to_step_length');
            $table->dropCheckConstraint('chk_state_transitions_from_step_length');
            $table->dropCheckConstraint('chk_state_transitions_channel_length');
            
            // Restore original foreign key
            $table->dropForeign(['state_id']);
            $table->foreign('state_id')
                  ->references('id')
                  ->on('application_states')
                  ->onDelete('cascade');
        });
    }
};
