version: '3.8'

services:
  # Application Service
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: bancozim-app
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - ./storage:/var/www/html/storage
      - ./public/uploads:/var/www/html/public/uploads
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - DB_HOST=database
      - DB_DATABASE=bancozim
      - DB_USERNAME=bancozim_user
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_HOST=redis
      - CACHE_DRIVER=redis
      - SESSION_DRIVER=redis
      - QUEUE_CONNECTION=redis
    depends_on:
      - database
      - redis
    networks:
      - bancozim-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Nginx Service
  nginx:
    image: nginx:alpine
    container_name: bancozim-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./public:/var/www/html/public:ro
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/sites-available:/etc/nginx/sites-available:ro
      - ./docker/ssl:/etc/nginx/ssl:ro
      - ./storage/logs/nginx:/var/log/nginx
    depends_on:
      - app
    networks:
      - bancozim-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/nginx-health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Database Service
  database:
    image: mysql:8.0
    container_name: bancozim-database
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: bancozim
      MYSQL_USER: bancozim_user
      MYSQL_PASSWORD: ${DB_PASSWORD}
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
    volumes:
      - database_data:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/custom.cnf:ro
      - ./docker/mysql/init:/docker-entrypoint-initdb.d:ro
    ports:
      - "3306:3306"
    networks:
      - bancozim-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    command: --default-authentication-plugin=mysql_native_password

  # Redis Service
  redis:
    image: redis:7-alpine
    container_name: bancozim-redis
    restart: unless-stopped
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/etc/redis/redis.conf:ro
    ports:
      - "6379:6379"
    networks:
      - bancozim-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: redis-server /etc/redis/redis.conf

  # Queue Worker Service
  queue:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: bancozim-queue
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - ./storage:/var/www/html/storage
    environment:
      - APP_ENV=production
      - DB_HOST=database
      - DB_DATABASE=bancozim
      - DB_USERNAME=bancozim_user
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_HOST=redis
      - QUEUE_CONNECTION=redis
    depends_on:
      - database
      - redis
    networks:
      - bancozim-network
    command: php artisan queue:work --sleep=3 --tries=3 --max-time=3600
    healthcheck:
      test: ["CMD", "php", "artisan", "queue:monitor"]
      interval: 60s
      timeout: 30s
      retries: 3

  # Scheduler Service
  scheduler:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: bancozim-scheduler
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - ./storage:/var/www/html/storage
    environment:
      - APP_ENV=production
      - DB_HOST=database
      - DB_DATABASE=bancozim
      - DB_USERNAME=bancozim_user
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_HOST=redis
    depends_on:
      - database
      - redis
    networks:
      - bancozim-network
    command: sh -c "while true; do php artisan schedule:run; sleep 60; done"

  # Monitoring Service (Optional)
  monitoring:
    image: prom/prometheus:latest
    container_name: bancozim-monitoring
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - bancozim-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Log Aggregation (Optional)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: bancozim-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - bancozim-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Backup Service
  backup:
    image: alpine:latest
    container_name: bancozim-backup
    restart: "no"
    volumes:
      - database_data:/backup/database:ro
      - ./storage:/backup/storage:ro
      - ./backups:/backups
    networks:
      - bancozim-network
    command: sh -c "
      apk add --no-cache mysql-client gzip tar &&
      while true; do
        echo 'Starting backup...' &&
        mysqldump -h database -u bancozim_user -p${DB_PASSWORD} bancozim | gzip > /backups/database_$(date +%Y%m%d_%H%M%S).sql.gz &&
        tar -czf /backups/storage_$(date +%Y%m%d_%H%M%S).tar.gz -C /backup storage &&
        find /backups -name '*.gz' -mtime +7 -delete &&
        echo 'Backup completed' &&
        sleep 86400;
      done"

# Networks
networks:
  bancozim-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Volumes
volumes:
  database_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  elasticsearch_data:
    driver: local
