<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\ProductController;

// Product API routes
Route::prefix('products')->group(function () {
    Route::get('/', [ProductController::class, 'index']); // Legacy endpoint
    Route::get('/categories', [ProductController::class, 'getCategories']);
    Route::get('/category/{categoryId}', [ProductController::class, 'getProductsByCategory']);
    Route::get('/product/{productId}', [ProductController::class, 'getProduct']);
    Route::get('/search', [ProductController::class, 'searchProducts']);
    Route::get('/statistics', [ProductController::class, 'getStatistics']);
});

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});
