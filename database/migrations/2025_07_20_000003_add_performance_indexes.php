<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add indexes to application_states table
        Schema::table('application_states', function (Blueprint $table) {
            // Composite indexes for common query patterns
            $table->index(['user_identifier', 'channel'], 'idx_user_channel');
            $table->index(['current_step', 'created_at'], 'idx_step_created');
            $table->index(['channel', 'current_step'], 'idx_channel_step');
            $table->index(['expires_at', 'current_step'], 'idx_expires_step');
            $table->index(['reference_code_expires_at'], 'idx_ref_code_expires');
            
            // Individual indexes for frequent lookups
            $table->index(['created_at'], 'idx_created_at');
            $table->index(['updated_at'], 'idx_updated_at');
            
            // JSON field indexes (MySQL 5.7+ / PostgreSQL)
            if (config('database.default') === 'mysql') {
                // MySQL JSON indexes
                $table->index([DB::raw("(JSON_EXTRACT(form_data, '$.formId'))")], 'idx_form_id');
                $table->index([DB::raw("(JSON_EXTRACT(form_data, '$.employer'))")], 'idx_employer');
                $table->index([DB::raw("(JSON_EXTRACT(form_data, '$.hasAccount'))")], 'idx_has_account');
                $table->index([DB::raw("(JSON_EXTRACT(form_data, '$.amount'))")], 'idx_amount');
                $table->index([DB::raw("(JSON_EXTRACT(form_data, '$.formResponses.emailAddress'))")], 'idx_email');
                $table->index([DB::raw("(JSON_EXTRACT(form_data, '$.formResponses.mobile'))")], 'idx_mobile');
                $table->index([DB::raw("(JSON_EXTRACT(form_data, '$.formResponses.nationalIdNumber'))")], 'idx_national_id');
            }
        });

        // Add indexes to state_transitions table
        Schema::table('state_transitions', function (Blueprint $table) {
            // Composite indexes for transition queries
            $table->index(['state_id', 'created_at'], 'idx_state_created');
            $table->index(['from_step', 'to_step'], 'idx_step_transition');
            $table->index(['channel', 'created_at'], 'idx_channel_created');
            $table->index(['to_step', 'created_at'], 'idx_to_step_created');
            
            // Individual indexes
            $table->index(['created_at'], 'idx_transition_created');
            $table->index(['transition_data'], 'idx_transition_data');
        });

        // Create indexes for potential future tables
        $this->createDocumentIndexes();
        $this->createAuditIndexes();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('application_states', function (Blueprint $table) {
            $table->dropIndex('idx_user_channel');
            $table->dropIndex('idx_step_created');
            $table->dropIndex('idx_channel_step');
            $table->dropIndex('idx_expires_step');
            $table->dropIndex('idx_ref_code_expires');
            $table->dropIndex('idx_created_at');
            $table->dropIndex('idx_updated_at');
            
            if (config('database.default') === 'mysql') {
                $table->dropIndex('idx_form_id');
                $table->dropIndex('idx_employer');
                $table->dropIndex('idx_has_account');
                $table->dropIndex('idx_amount');
                $table->dropIndex('idx_email');
                $table->dropIndex('idx_mobile');
                $table->dropIndex('idx_national_id');
            }
        });

        Schema::table('state_transitions', function (Blueprint $table) {
            $table->dropIndex('idx_state_created');
            $table->dropIndex('idx_step_transition');
            $table->dropIndex('idx_channel_created');
            $table->dropIndex('idx_to_step_created');
            $table->dropIndex('idx_transition_created');
            $table->dropIndex('idx_transition_data');
        });
    }

    /**
     * Create indexes for documents table (if it exists)
     */
    private function createDocumentIndexes(): void
    {
        if (Schema::hasTable('documents')) {
            Schema::table('documents', function (Blueprint $table) {
                $table->index(['application_state_id', 'document_type'], 'idx_app_doc_type');
                $table->index(['document_type', 'created_at'], 'idx_doc_type_created');
                $table->index(['file_path'], 'idx_file_path');
                $table->index(['file_size'], 'idx_file_size');
                $table->index(['mime_type'], 'idx_mime_type');
                $table->index(['is_validated'], 'idx_is_validated');
            });
        }
    }

    /**
     * Create indexes for audit table (if it exists)
     */
    private function createAuditIndexes(): void
    {
        if (Schema::hasTable('audit_logs')) {
            Schema::table('audit_logs', function (Blueprint $table) {
                $table->index(['auditable_type', 'auditable_id'], 'idx_auditable');
                $table->index(['event', 'created_at'], 'idx_event_created');
                $table->index(['user_id', 'created_at'], 'idx_user_created');
                $table->index(['ip_address'], 'idx_ip_address');
                $table->index(['user_agent'], 'idx_user_agent');
            });
        }
    }
};
