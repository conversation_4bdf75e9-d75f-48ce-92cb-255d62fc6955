import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { ChevronLeft, Building, DollarSign, Users, Shield } from 'lucide-react';

interface SMEBusinessFormProps {
    data: any;
    onNext: (data: any) => void;
    onBack: () => void;
    loading?: boolean;
}

const SMEBusinessForm: React.FC<SMEBusinessFormProps> = ({ data, onNext, onBack, loading }) => {
    // Calculate credit facility details from product selection
    const calculateCreditFacilityDetails = () => {
        const businessName = data.business; // string from ProductSelection
        const finalPrice = data.amount || 0; // number from ProductSelection
        const intent = data.intent || 'hirePurchase';
        
        let facilityType = '';
        if (intent === 'hirePurchase' && businessName) {
            facilityType = `Hire Purchase Credit - ${businessName}`;
        } else if ((intent === 'microBiz' || intent === 'microBizLoan') && businessName) {
            facilityType = `Micro Biz Loan - ${businessName}`;
        } else if (businessName) {
            // Fallback if intent doesn't match
            facilityType = `Credit Facility - ${businessName}`;
        }
        
        // Calculate tenure based on amount
        let tenure = 12; // default
        if (finalPrice <= 1000) tenure = 6;
        else if (finalPrice <= 5000) tenure = 12;
        else if (finalPrice <= 15000) tenure = 18;
        else tenure = 24;
        
        // Calculate monthly payment (10% annual interest)
        const interestRate = 0.10;
        const monthlyInterestRate = interestRate / 12;
        const monthlyPayment = finalPrice > 0 ? 
            (finalPrice * monthlyInterestRate * Math.pow(1 + monthlyInterestRate, tenure)) /
            (Math.pow(1 + monthlyInterestRate, tenure) - 1) : 0;
        
        return {
            creditFacilityType: facilityType,
            loanAmount: finalPrice.toFixed(2),
            loanTenure: tenure.toString(),
            monthlyPayment: monthlyPayment.toFixed(2),
            interestRate: '10.0'
        };
    };
    
    const creditDetails = calculateCreditFacilityDetails();
    const businessName = data.business;
    
    const [formData, setFormData] = useState({
        // Credit Facility Details (pre-populated)
        ...creditDetails,
        
        // Business Type
        businessType: '', // Company, PBC, Informal body
        loanType: '',
        
        // Business Information
        registeredName: '',
        tradingName: '',
        typeOfBusiness: '',
        businessAddress: '',
        periodAtLocation: '',
        initialCapital: '',
        incorporationDate: '',
        incorporationNumber: '',
        bpNumber: '',
        contactPhone: '',
        emailAddress: '',
        yearsInBusiness: '',
        
        // Capital Sources
        capitalSources: {
            ownSavings: false,
            familyGift: false,
            loan: false,
            other: false,
            otherSpecify: ''
        },
        
        // Customer Base
        customerBase: {
            individuals: false,
            businesses: false,
            other: false,
            otherSpecify: ''
        },
        
        // Financial Information
        estimatedAnnualSales: '',
        netProfit: '',
        totalLiabilities: '',
        netCashFlow: '',
        mainProducts: '',
        mainProblems: '',
        
        // Other Business Interests
        otherBusinessName: '',
        otherBusinessAddress: '',
        otherBusinessPhone: '',
        numberOfEmployees: {
            fullTime: '',
            partTime: '',
            nonPaid: '',
            total: ''
        },
        customerLocation: {
            neighborhood: false,
            thisTown: false,
            other: false,
            otherSpecify: ''
        },
        
        // Purpose of loan (auto-populated from product selection)
        purposeOfLoan: businessName ? `${businessName} - ${data.scale || 'Standard Scale'}` : '',
        
        // Budget Breakdown
        budgetItems: [
            { item: '', cost: '' },
            { item: '', cost: '' },
            { item: '', cost: '' }
        ],
        
        // Directors' Personal Details
        directorsPersonalDetails: {
            title: '',
            firstName: '',
            surname: '',
            maidenName: '',
            gender: '',
            dateOfBirth: '',
            maritalStatus: '',
            nationality: '',
            idNumber: '',
            cellNumber: '',
            whatsApp: '',
            highestEducation: '',
            citizenship: '',
            emailAddress: '',
            residentialAddress: '',
            passportPhoto: '',
            periodAtCurrentAddress: { years: '', months: '' },
            periodAtPreviousAddress: { years: '', months: '' }
        },
        
        // Spouse and Next of Kin
        spouseAndNextOfKin: {
            spouse: {
                fullName: '',
                phoneNumber: '',
                emailAddress: '',
                address: ''
            },
            nextOfKin1: {
                fullName: '',
                relationship: '',
                phoneNumber: '',
                emailAddress: '',
                address: ''
            },
            nextOfKin2: {
                fullName: '',
                relationship: '',
                phoneNumber: '',
                emailAddress: '',
                address: ''
            }
        },
        
        // Employment Details
        employmentDetails: {
            businessEmployerName: '',
            jobTitle: '',
            businessEmployerAddress: '',
            dateOfEmployment: '',
            immediateManager: '',
            phoneNumberOfManager: ''
        },
        
        // Property Ownership
        propertyOwnership: '',
        
        // Banking Details
        bankingDetails: {
            bank: '',
            branch: '',
            accountNumber: ''
        },
        
        // Loans with Other Institutions
        otherLoans: [
            { institution: '', monthlyInstallment: '', currentBalance: '', maturityDate: '' },
            { institution: '', monthlyInstallment: '', currentBalance: '', maturityDate: '' }
        ],
        
        // References
        references: [
            { name: '', phoneNumber: '' },
            { name: '', phoneNumber: '' },
            { name: '', phoneNumber: '' }
        ],
        
        // Security (Assets Pledged)
        securityAssets: [
            { description: '', serialNumber: '', estimatedValue: '' },
            { description: '', serialNumber: '', estimatedValue: '' },
            { description: '', serialNumber: '', estimatedValue: '' }
        ],
        
        // Declaration
        declaration: {
            acknowledged: false
        },
        
        // Directors Signatures
        directorsSignatures: [
            { name: '', signature: '', date: '' },
            { name: '', signature: '', date: '' },
            { name: '', signature: '', date: '' },
            { name: '', signature: '', date: '' },
            { name: '', signature: '', date: '' }
        ],
        
        // KYC Documents
        kycDocuments: {
            copyOfId: false,
            articlesOfAssociation: false,
            bankStatement: false,
            groupConstitution: false,
            proofOfResidence: false,
            financialStatement: false,
            certificateOfIncorporation: false,
            ecocashStatements: false,
            resolutionToBorrow: false,
            cr11: false,
            cr6: false,
            cr5: false,
            moa: false
        }
    });

    const handleInputChange = (field: string, value: string) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleNestedChange = (section: string, field: string, value: string | boolean) => {
        setFormData(prev => ({
            ...prev,
            [section]: {
                ...prev[section],
                [field]: value
            }
        }));
    };

    const handleArrayChange = (section: string, index: number, field: string, value: string) => {
        setFormData(prev => ({
            ...prev,
            [section]: prev[section].map((item, i) => 
                i === index ? { ...item, [field]: value } : item
            )
        }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onNext(formData);
    };

    return (
        <div className="max-w-4xl mx-auto space-y-6">
            <div className="text-center">
                <h2 className="text-2xl font-semibold mb-2">SME Business Application Form</h2>
                <p className="text-gray-600 dark:text-gray-400">
                    Complete your business loan application
                </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
                {/* Business Type */}
                <Card className="p-6">
                    <div className="flex items-center mb-4">
                        <Building className="h-6 w-6 text-emerald-600 mr-3" />
                        <h3 className="text-lg font-semibold">Business Type</h3>
                    </div>
                    
                    <div className="grid gap-4 md:grid-cols-3">
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="company"
                                checked={formData.businessType === 'company'}
                                onCheckedChange={(checked) => checked && handleInputChange('businessType', 'company')}
                            />
                            <Label htmlFor="company">Company</Label>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="pbc"
                                checked={formData.businessType === 'pbc'}
                                onCheckedChange={(checked) => checked && handleInputChange('businessType', 'pbc')}
                            />
                            <Label htmlFor="pbc">PBC</Label>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="informal"
                                checked={formData.businessType === 'informal'}
                                onCheckedChange={(checked) => checked && handleInputChange('businessType', 'informal')}
                            />
                            <Label htmlFor="informal">Informal body</Label>
                        </div>
                        
                        <div className="md:col-span-3">
                            <Label htmlFor="loanType">Loan Type</Label>
                            <Input
                                id="loanType"
                                value={formData.loanType}
                                onChange={(e) => handleInputChange('loanType', e.target.value)}
                                placeholder="Enter loan type"
                            />
                        </div>
                    </div>
                </Card>

                {/* Business Information */}
                <Card className="p-6">
                    <div className="flex items-center mb-4">
                        <Building className="h-6 w-6 text-emerald-600 mr-3" />
                        <h3 className="text-lg font-semibold">Business Information</h3>
                    </div>
                    
                    <div className="grid gap-4 md:grid-cols-2">
                        <div>
                            <Label htmlFor="registeredName">Registered Name</Label>
                            <Input
                                id="registeredName"
                                value={formData.registeredName}
                                onChange={(e) => handleInputChange('registeredName', e.target.value)}
                                required
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="tradingName">Trading Name</Label>
                            <Input
                                id="tradingName"
                                value={formData.tradingName}
                                onChange={(e) => handleInputChange('tradingName', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="typeOfBusiness">Type of Business</Label>
                            <Input
                                id="typeOfBusiness"
                                value={formData.typeOfBusiness}
                                onChange={(e) => handleInputChange('typeOfBusiness', e.target.value)}
                                required
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="businessAddress">Business Address</Label>
                            <Input
                                id="businessAddress"
                                value={formData.businessAddress}
                                onChange={(e) => handleInputChange('businessAddress', e.target.value)}
                                required
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="periodAtLocation">Period at Current Business Location</Label>
                            <Input
                                id="periodAtLocation"
                                value={formData.periodAtLocation}
                                onChange={(e) => handleInputChange('periodAtLocation', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="initialCapital">Amount of Initial Capital</Label>
                            <Input
                                id="initialCapital"
                                type="number"
                                value={formData.initialCapital}
                                onChange={(e) => handleInputChange('initialCapital', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="incorporationDate">Incorporation Date</Label>
                            <Input
                                id="incorporationDate"
                                type="date"
                                value={formData.incorporationDate}
                                onChange={(e) => handleInputChange('incorporationDate', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="incorporationNumber">Certificate of Incorporation Number</Label>
                            <Input
                                id="incorporationNumber"
                                value={formData.incorporationNumber}
                                onChange={(e) => handleInputChange('incorporationNumber', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="contactPhone">Contact Phone Number</Label>
                            <Input
                                id="contactPhone"
                                value={formData.contactPhone}
                                onChange={(e) => handleInputChange('contactPhone', e.target.value)}
                                required
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="emailAddress">Email Address</Label>
                            <Input
                                id="emailAddress"
                                type="email"
                                value={formData.emailAddress}
                                onChange={(e) => handleInputChange('emailAddress', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="yearsInBusiness">Number of Years in Business</Label>
                            <Input
                                id="yearsInBusiness"
                                type="number"
                                value={formData.yearsInBusiness}
                                onChange={(e) => handleInputChange('yearsInBusiness', e.target.value)}
                            />
                        </div>
                    </div>
                </Card>

                {/* Sources of Capital */}
                <Card className="p-6">
                    <div className="flex items-center mb-4">
                        <DollarSign className="h-6 w-6 text-emerald-600 mr-3" />
                        <h3 className="text-lg font-semibold">Sources of Capital</h3>
                    </div>
                    
                    <div className="grid gap-4 md:grid-cols-2">
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="ownSavings"
                                checked={formData.capitalSources.ownSavings}
                                onCheckedChange={(checked) => handleNestedChange('capitalSources', 'ownSavings', checked)}
                            />
                            <Label htmlFor="ownSavings">Own Savings</Label>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="familyGift"
                                checked={formData.capitalSources.familyGift}
                                onCheckedChange={(checked) => handleNestedChange('capitalSources', 'familyGift', checked)}
                            />
                            <Label htmlFor="familyGift">Family Gift</Label>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="loan"
                                checked={formData.capitalSources.loan}
                                onCheckedChange={(checked) => handleNestedChange('capitalSources', 'loan', checked)}
                            />
                            <Label htmlFor="loan">Loan</Label>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="capitalOther"
                                checked={formData.capitalSources.other}
                                onCheckedChange={(checked) => handleNestedChange('capitalSources', 'other', checked)}
                            />
                            <Label htmlFor="capitalOther">Other</Label>
                        </div>
                        
                        {formData.capitalSources.other && (
                            <div className="md:col-span-2">
                                <Label htmlFor="capitalOtherSpecify">Please specify</Label>
                                <Input
                                    id="capitalOtherSpecify"
                                    value={formData.capitalSources.otherSpecify}
                                    onChange={(e) => handleNestedChange('capitalSources', 'otherSpecify', e.target.value)}
                                />
                            </div>
                        )}
                    </div>
                </Card>

                {/* Financial Information */}
                <Card className="p-6">
                    <div className="flex items-center mb-4">
                        <DollarSign className="h-6 w-6 text-emerald-600 mr-3" />
                        <h3 className="text-lg font-semibold">Financial Information</h3>
                    </div>
                    
                    <div className="grid gap-4 md:grid-cols-2">
                        <div>
                            <Label htmlFor="estimatedAnnualSales">Estimated Annual Sales</Label>
                            <Input
                                id="estimatedAnnualSales"
                                type="number"
                                value={formData.estimatedAnnualSales}
                                onChange={(e) => handleInputChange('estimatedAnnualSales', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="netProfit">Net Profit</Label>
                            <Input
                                id="netProfit"
                                type="number"
                                value={formData.netProfit}
                                onChange={(e) => handleInputChange('netProfit', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="totalLiabilities">Total Liabilities</Label>
                            <Input
                                id="totalLiabilities"
                                type="number"
                                value={formData.totalLiabilities}
                                onChange={(e) => handleInputChange('totalLiabilities', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="netCashFlow">Net Cash Flow</Label>
                            <Input
                                id="netCashFlow"
                                type="number"
                                value={formData.netCashFlow}
                                onChange={(e) => handleInputChange('netCashFlow', e.target.value)}
                            />
                        </div>
                        
                        <div className="md:col-span-2">
                            <Label htmlFor="mainProducts">Main Product/Services</Label>
                            <Textarea
                                id="mainProducts"
                                value={formData.mainProducts}
                                onChange={(e) => handleInputChange('mainProducts', e.target.value)}
                                rows={3}
                            />
                        </div>
                        
                        <div className="md:col-span-2">
                            <Label htmlFor="mainProblems">Main Problems Faced by Business</Label>
                            <Textarea
                                id="mainProblems"
                                value={formData.mainProblems}
                                onChange={(e) => handleInputChange('mainProblems', e.target.value)}
                                rows={3}
                            />
                        </div>
                    </div>
                </Card>

                {/* Credit Facility Application Details */}
                <Card className="p-6 bg-green-50 dark:bg-green-900 border-green-200 dark:border-green-700">
                    <div className="flex items-center mb-4">
                        <DollarSign className="h-6 w-6 text-green-600 mr-3" />
                        <h3 className="text-lg font-semibold text-green-800 dark:text-green-200">Credit Facility Application Details</h3>
                    </div>
                    
                    {/* Pre-populated readonly fields */}
                    <div className="grid gap-4 mb-6 p-4 bg-white dark:bg-gray-800 rounded-lg border">
                        <div className="text-sm text-green-600 dark:text-green-400 font-medium mb-2">
                            ✅ The following details have been automatically filled based on your product selection:
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <Label className="text-gray-700 dark:text-gray-300">Credit Facility Type</Label>
                                <Input
                                    value={formData.creditFacilityType}
                                    readOnly
                                    className="bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600"
                                />
                            </div>
                            <div>
                                <Label className="text-gray-700 dark:text-gray-300">Loan Amount (USD)</Label>
                                <div className="relative">
                                    <span className="absolute left-3 top-2.5 text-gray-500">$</span>
                                    <Input
                                        value={formData.loanAmount}
                                        readOnly
                                        className="pl-8 bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600"
                                    />
                                </div>
                            </div>
                            <div>
                                <Label className="text-gray-700 dark:text-gray-300">Loan Tenure (Months)</Label>
                                <Input
                                    value={`${formData.loanTenure} months`}
                                    readOnly
                                    className="bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600"
                                />
                            </div>
                            <div>
                                <Label className="text-gray-700 dark:text-gray-300">Monthly Payment (USD)</Label>
                                <div className="relative">
                                    <span className="absolute left-3 top-2.5 text-gray-500">$</span>
                                    <Input
                                        value={formData.monthlyPayment}
                                        readOnly
                                        className="pl-8 bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600"
                                    />
                                </div>
                            </div>
                            <div>
                                <Label className="text-gray-700 dark:text-gray-300">Interest Rate (%)</Label>
                                <Input
                                    value={`${formData.interestRate}%`}
                                    readOnly
                                    className="bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600"
                                />
                            </div>
                        </div>
                    </div>
                    
                    {/* Editable purpose field */}
                    <div>
                        <Label htmlFor="purposeOfLoan">Purpose/Asset Applied For</Label>
                        <Textarea
                            id="purposeOfLoan"
                            value={formData.purposeOfLoan}
                            onChange={(e) => handleInputChange('purposeOfLoan', e.target.value)}
                            rows={3}
                            required
                            placeholder="Describe the purpose and asset details..."
                        />
                    </div>
                </Card>

                {/* References */}
                <Card className="p-6">
                    <div className="flex items-center mb-4">
                        <Users className="h-6 w-6 text-emerald-600 mr-3" />
                        <h3 className="text-lg font-semibold">References</h3>
                    </div>
                    
                    {formData.references.map((reference, index) => (
                        <div key={index} className="grid gap-4 md:grid-cols-2 mb-4 p-4 border rounded-lg">
                            <div>
                                <Label htmlFor={`ref-${index}-name`}>Name</Label>
                                <Input
                                    id={`ref-${index}-name`}
                                    value={reference.name}
                                    onChange={(e) => handleArrayChange('references', index, 'name', e.target.value)}
                                />
                            </div>
                            
                            <div>
                                <Label htmlFor={`ref-${index}-phone`}>Phone Number</Label>
                                <Input
                                    id={`ref-${index}-phone`}
                                    value={reference.phoneNumber}
                                    onChange={(e) => handleArrayChange('references', index, 'phoneNumber', e.target.value)}
                                />
                            </div>
                        </div>
                    ))}
                </Card>

                {/* Directors' Personal Details */}
                <Card className="p-6">
                    <div className="bg-emerald-100 p-4 rounded-lg mb-4">
                        <h3 className="text-lg font-semibold text-emerald-800">DIRECTORS' PERSONAL DETAILS</h3>
                    </div>
                    
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        <div>
                            <Label htmlFor="directorTitle">Title (Mr./Mrs./Dr/Prof)</Label>
                            <Input
                                id="directorTitle"
                                value={formData.directorsPersonalDetails.title}
                                onChange={(e) => handleNestedChange('directorsPersonalDetails', 'title', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="directorFirstName">First Name</Label>
                            <Input
                                id="directorFirstName"
                                value={formData.directorsPersonalDetails.firstName}
                                onChange={(e) => handleNestedChange('directorsPersonalDetails', 'firstName', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="directorSurname">Surname</Label>
                            <Input
                                id="directorSurname"
                                value={formData.directorsPersonalDetails.surname}
                                onChange={(e) => handleNestedChange('directorsPersonalDetails', 'surname', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="directorMaidenName">Maiden Name</Label>
                            <Input
                                id="directorMaidenName"
                                value={formData.directorsPersonalDetails.maidenName}
                                onChange={(e) => handleNestedChange('directorsPersonalDetails', 'maidenName', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="directorGender">Gender</Label>
                            <Input
                                id="directorGender"
                                value={formData.directorsPersonalDetails.gender}
                                onChange={(e) => handleNestedChange('directorsPersonalDetails', 'gender', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="directorDateOfBirth">Date Of Birth</Label>
                            <Input
                                id="directorDateOfBirth"
                                type="date"
                                value={formData.directorsPersonalDetails.dateOfBirth}
                                onChange={(e) => handleNestedChange('directorsPersonalDetails', 'dateOfBirth', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="directorMaritalStatus">Marital Status</Label>
                            <Input
                                id="directorMaritalStatus"
                                value={formData.directorsPersonalDetails.maritalStatus}
                                onChange={(e) => handleNestedChange('directorsPersonalDetails', 'maritalStatus', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="directorNationality">Nationality</Label>
                            <Input
                                id="directorNationality"
                                value={formData.directorsPersonalDetails.nationality}
                                onChange={(e) => handleNestedChange('directorsPersonalDetails', 'nationality', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="directorIdNumber">ID Number</Label>
                            <Input
                                id="directorIdNumber"
                                value={formData.directorsPersonalDetails.idNumber}
                                onChange={(e) => handleNestedChange('directorsPersonalDetails', 'idNumber', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="directorCellNumber">Cell Number</Label>
                            <Input
                                id="directorCellNumber"
                                value={formData.directorsPersonalDetails.cellNumber}
                                onChange={(e) => handleNestedChange('directorsPersonalDetails', 'cellNumber', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="directorWhatsApp">WhatsApp</Label>
                            <Input
                                id="directorWhatsApp"
                                value={formData.directorsPersonalDetails.whatsApp}
                                onChange={(e) => handleNestedChange('directorsPersonalDetails', 'whatsApp', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="directorEducation">Highest Educational Qualification</Label>
                            <Input
                                id="directorEducation"
                                value={formData.directorsPersonalDetails.highestEducation}
                                onChange={(e) => handleNestedChange('directorsPersonalDetails', 'highestEducation', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="directorCitizenship">Citizenship</Label>
                            <Input
                                id="directorCitizenship"
                                value={formData.directorsPersonalDetails.citizenship}
                                onChange={(e) => handleNestedChange('directorsPersonalDetails', 'citizenship', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="directorEmail">Email Address</Label>
                            <Input
                                id="directorEmail"
                                type="email"
                                value={formData.directorsPersonalDetails.emailAddress}
                                onChange={(e) => handleNestedChange('directorsPersonalDetails', 'emailAddress', e.target.value)}
                            />
                        </div>
                        
                        <div className="lg:col-span-2">
                            <Label htmlFor="directorAddress">Residential Address</Label>
                            <Textarea
                                id="directorAddress"
                                value={formData.directorsPersonalDetails.residentialAddress}
                                onChange={(e) => handleNestedChange('directorsPersonalDetails', 'residentialAddress', e.target.value)}
                                rows={3}
                            />
                        </div>
                        
                        <div>
                            <Label>Passport Photo</Label>
                            <div className="h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center text-gray-500">
                                Photo Area
                            </div>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-2">
                            <div>
                                <Label>Period at Current Address</Label>
                                <div className="flex gap-2">
                                    <Input placeholder="Years" />
                                    <Input placeholder="Months" />
                                </div>
                            </div>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-2">
                            <div>
                                <Label>Period at Previous Address</Label>
                                <div className="flex gap-2">
                                    <Input placeholder="Years" />
                                    <Input placeholder="Months" />
                                </div>
                            </div>
                        </div>
                    </div>
                </Card>

                {/* Spouse and Next of Kin Details */}
                <Card className="p-6">
                    <div className="bg-emerald-100 p-4 rounded-lg mb-4">
                        <h3 className="text-lg font-semibold text-emerald-800">SPOUSE AND NEXT OF KIN DETAILS</h3>
                    </div>
                    
                    <div className="grid gap-6 md:grid-cols-2">
                        {/* Spouse Details */}
                        <div>
                            <h4 className="font-semibold mb-3 text-emerald-700">SPOUSE DETAILS</h4>
                            <div className="space-y-3">
                                <div>
                                    <Label htmlFor="spouseFullName">Full Name</Label>
                                    <Input id="spouseFullName" />
                                </div>
                                <div>
                                    <Label htmlFor="spousePhone">Phone Number</Label>
                                    <Input id="spousePhone" />
                                </div>
                                <div>
                                    <Label htmlFor="spouseEmail">Email Address</Label>
                                    <Input id="spouseEmail" type="email" />
                                </div>
                                <div>
                                    <Label htmlFor="spouseAddress">Address</Label>
                                    <Textarea id="spouseAddress" rows={2} />
                                </div>
                            </div>
                        </div>
                        
                        {/* Next of Kin 1 */}
                        <div>
                            <h4 className="font-semibold mb-3 text-emerald-700">NEXT OF KIN 1</h4>
                            <div className="space-y-3">
                                <div>
                                    <Label htmlFor="nextOfKin1Name">Full Name</Label>
                                    <Input id="nextOfKin1Name" />
                                </div>
                                <div>
                                    <Label htmlFor="nextOfKin1Relationship">Relationship</Label>
                                    <Input id="nextOfKin1Relationship" />
                                </div>
                                <div>
                                    <Label htmlFor="nextOfKin1Phone">Phone Number</Label>
                                    <Input id="nextOfKin1Phone" />
                                </div>
                                <div>
                                    <Label htmlFor="nextOfKin1Email">Email Address</Label>
                                    <Input id="nextOfKin1Email" type="email" />
                                </div>
                                <div>
                                    <Label htmlFor="nextOfKin1Address">Address</Label>
                                    <Textarea id="nextOfKin1Address" rows={2} />
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    {/* Next of Kin 2 */}
                    <div className="mt-6">
                        <h4 className="font-semibold mb-3 text-emerald-700">NEXT OF KIN 2</h4>
                        <div className="grid gap-4 md:grid-cols-4">
                            <div>
                                <Label htmlFor="nextOfKin2Name">Full Name</Label>
                                <Input id="nextOfKin2Name" />
                            </div>
                            <div>
                                <Label htmlFor="nextOfKin2Relationship">Relationship</Label>
                                <Input id="nextOfKin2Relationship" />
                            </div>
                            <div>
                                <Label htmlFor="nextOfKin2Phone">Phone Number</Label>
                                <Input id="nextOfKin2Phone" />
                            </div>
                            <div>
                                <Label htmlFor="nextOfKin2Email">Email Address</Label>
                                <Input id="nextOfKin2Email" type="email" />
                            </div>
                        </div>
                        <div className="mt-3">
                            <Label htmlFor="nextOfKin2Address">Address</Label>
                            <Textarea id="nextOfKin2Address" rows={2} />
                        </div>
                    </div>
                </Card>

                {/* Employment Details */}
                <Card className="p-6">
                    <div className="bg-emerald-100 p-4 rounded-lg mb-4">
                        <h3 className="text-lg font-semibold text-emerald-800">EMPLOYMENT DETAILS</h3>
                    </div>
                    
                    <div className="grid gap-4 md:grid-cols-2">
                        <div>
                            <Label htmlFor="businessEmployerName">Business/Employer's Name</Label>
                            <Input id="businessEmployerName" />
                        </div>
                        <div>
                            <Label htmlFor="jobTitleEmployment">Job Title</Label>
                            <Input id="jobTitleEmployment" />
                        </div>
                        <div>
                            <Label htmlFor="businessEmployerAddress">Business/Employer's Address</Label>
                            <Input id="businessEmployerAddress" />
                        </div>
                        <div>
                            <Label htmlFor="dateOfEmploymentDirector">Date of Employment</Label>
                            <Input id="dateOfEmploymentDirector" type="date" />
                        </div>
                        <div>
                            <Label htmlFor="immediateManager">Name of Immediate Manager</Label>
                            <Input id="immediateManager" />
                        </div>
                        <div>
                            <Label htmlFor="managerPhone">Phone Number of Immediate Manager</Label>
                            <Input id="managerPhone" />
                        </div>
                    </div>
                </Card>

                {/* Property Ownership */}
                <Card className="p-6">
                    <div className="bg-emerald-100 p-4 rounded-lg mb-4">
                        <h3 className="text-lg font-semibold text-emerald-800">PROPERTY OWNERSHIP</h3>
                    </div>
                    
                    <div className="flex gap-4">
                        {['Rented', 'Employer Owned', 'Mortgaged', 'Owned Without Mortgage', 'Parents owned'].map((option) => (
                            <label key={option} className="flex items-center space-x-2">
                                <Checkbox
                                    checked={formData.propertyOwnership === option}
                                    onCheckedChange={(checked) => checked && handleInputChange('propertyOwnership', option)}
                                />
                                <span className="text-sm">{option}</span>
                            </label>
                        ))}
                    </div>
                </Card>

                {/* Banking/Mobile Account Details */}
                <Card className="p-6">
                    <div className="bg-emerald-100 p-4 rounded-lg mb-4">
                        <h3 className="text-lg font-semibold text-emerald-800">BANKING/MOBILE ACCOUNT DETAILS</h3>
                    </div>
                    
                    <div className="grid gap-4 md:grid-cols-3">
                        <div>
                            <Label htmlFor="bankName">Bank</Label>
                            <Input id="bankName" />
                        </div>
                        <div>
                            <Label htmlFor="bankBranch">Branch</Label>
                            <Input id="bankBranch" />
                        </div>
                        <div>
                            <Label htmlFor="bankAccountNumber">Account Number</Label>
                            <Input id="bankAccountNumber" />
                        </div>
                    </div>
                </Card>

                {/* Loans with Other Institutions */}
                <Card className="p-6">
                    <div className="bg-emerald-100 p-4 rounded-lg mb-4">
                        <h3 className="text-lg font-semibold text-emerald-800">LOANS WITH OTHER INSTITUTIONS (ALSO INCLUDE QUPA LOAN)</h3>
                    </div>
                    
                    <div className="overflow-x-auto">
                        <table className="w-full border-collapse border border-gray-300">
                            <thead>
                                <tr className="bg-gray-50">
                                    <th className="border border-gray-300 p-2 text-left">INSTITUTION</th>
                                    <th className="border border-gray-300 p-2 text-left">MONTHLY INSTALLMENT</th>
                                    <th className="border border-gray-300 p-2 text-left">CURRENT LOAN BALANCE</th>
                                    <th className="border border-gray-300 p-2 text-left">MATURITY DATE</th>
                                </tr>
                            </thead>
                            <tbody>
                                {[...Array(3)].map((_, index) => (
                                    <tr key={index}>
                                        <td className="border border-gray-300 p-2">
                                            <Input className="w-full" />
                                        </td>
                                        <td className="border border-gray-300 p-2">
                                            <Input className="w-full" type="number" />
                                        </td>
                                        <td className="border border-gray-300 p-2">
                                            <Input className="w-full" type="number" />
                                        </td>
                                        <td className="border border-gray-300 p-2">
                                            <Input className="w-full" type="date" />
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </Card>

                {/* Declaration */}
                <Card className="p-6">
                    <div className="bg-emerald-100 p-4 rounded-lg mb-4">
                        <h3 className="text-lg font-semibold text-emerald-800">DECLARATION</h3>
                    </div>
                    
                    <div className="space-y-4">
                        <p className="text-sm text-gray-700">
                            We declare that the information given above is accurate and correct. We are aware that falsifying information automatically leads to decline of our loan application. We authorise Qupa Microfinance to obtain and use the information obtained for the purposes of this application with any recognised credit bureau. We authorise Qupa microfinance to references from friends, relatives, neighbours and business partners including visits to our homes and verification of my assets. We have read and fully understood the above together with all the conditions, and We agree to be bound by Qupa Micro-Finance terms and conditions.
                        </p>
                        
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="declarationAcknowledged"
                                checked={formData.declaration.acknowledged}
                                onCheckedChange={(checked) => handleNestedChange('declaration', 'acknowledged', checked)}
                            />
                            <Label htmlFor="declarationAcknowledged">I acknowledge and agree to the above declaration</Label>
                        </div>
                    </div>
                </Card>

                {/* Directors Signature */}
                <Card className="p-6">
                    <div className="bg-emerald-100 p-4 rounded-lg mb-4">
                        <h3 className="text-lg font-semibold text-emerald-800">DIRECTORS SIGNATURE</h3>
                    </div>
                    
                    {formData.directorsSignatures.map((director, index) => (
                        <div key={index} className="grid gap-4 md:grid-cols-4 mb-4 p-4 border rounded-lg">
                            <div>
                                <Label htmlFor={`director-${index}-name`}>Director: Name</Label>
                                <Input
                                    id={`director-${index}-name`}
                                    value={director.name}
                                    onChange={(e) => handleArrayChange('directorsSignatures', index, 'name', e.target.value)}
                                />
                            </div>
                            
                            <div>
                                <Label htmlFor={`director-${index}-signature`}>Signature</Label>
                                <div className="h-10 border-2 border-dashed border-gray-300 rounded flex items-center justify-center text-gray-500 text-sm">
                                    Signature Area
                                </div>
                            </div>
                            
                            <div>
                                <Label htmlFor={`director-${index}-date`}>Date</Label>
                                <Input
                                    id={`director-${index}-date`}
                                    type="date"
                                    value={director.date}
                                    onChange={(e) => handleArrayChange('directorsSignatures', index, 'date', e.target.value)}
                                />
                            </div>
                        </div>
                    ))}
                </Card>

                {/* KYC Checklist */}
                <Card className="p-6">
                    <div className="bg-emerald-100 p-4 rounded-lg mb-4">
                        <h3 className="text-lg font-semibold text-emerald-800">KYC CHECKLIST</h3>
                    </div>
                    
                    <div className="grid gap-3 md:grid-cols-2">
                        <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                                <Checkbox id="copyOfId" />
                                <Label htmlFor="copyOfId">Copy of ID, License, Valid Passport</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                                <Checkbox id="articlesOfAssociation" />
                                <Label htmlFor="articlesOfAssociation">Articles of association/PBC</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                                <Checkbox id="bankStatement" />
                                <Label htmlFor="bankStatement">Stamped 3 months' Bank Statement</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                                <Checkbox id="groupConstitution" />
                                <Label htmlFor="groupConstitution">Group Constitution</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                                <Checkbox id="proofOfResidence" />
                                <Label htmlFor="proofOfResidence">Proof of Residence/Confirmation Letter</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                                <Checkbox id="financialStatement" />
                                <Label htmlFor="financialStatement">Financial Statement</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                                <Checkbox id="certificateOfIncorporation" />
                                <Label htmlFor="certificateOfIncorporation">Certificate of Incorporation</Label>
                            </div>
                        </div>
                        
                        <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                                <Checkbox id="ecocashStatements" />
                                <Label htmlFor="ecocashStatements">Ecocash Statements where applicable</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                                <Checkbox id="resolutionToBorrow" />
                                <Label htmlFor="resolutionToBorrow">Resolution to borrow</Label>
                            </div>
                            
                            <div className="mt-4">
                                <p className="font-medium mb-2">Company documents:</p>
                                <div className="space-y-2">
                                    <div className="flex items-center space-x-2">
                                        <Checkbox id="cr11" />
                                        <Label htmlFor="cr11">CR11</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <Checkbox id="cr6" />
                                        <Label htmlFor="cr6">CR6</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <Checkbox id="cr5" />
                                        <Label htmlFor="cr5">CR5</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <Checkbox id="moa" />
                                        <Label htmlFor="moa">MOA</Label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div className="mt-6 flex justify-end">
                        <div className="w-40 h-20 border-2 border-gray-300 rounded flex items-center justify-center text-gray-500">
                            Qupa Date Stamp:
                        </div>
                    </div>
                </Card>

                {/* Security (Assets Pledged) */}
                <Card className="p-6">
                    <div className="flex items-center mb-4">
                        <Shield className="h-6 w-6 text-emerald-600 mr-3" />
                        <h3 className="text-lg font-semibold">Security (Assets Pledged)</h3>
                    </div>
                    
                    {formData.securityAssets.map((asset, index) => (
                        <div key={index} className="grid gap-4 md:grid-cols-3 mb-4 p-4 border rounded-lg">
                            <div>
                                <Label htmlFor={`asset-${index}-description`}>Description</Label>
                                <Input
                                    id={`asset-${index}-description`}
                                    value={asset.description}
                                    onChange={(e) => handleArrayChange('securityAssets', index, 'description', e.target.value)}
                                />
                            </div>
                            
                            <div>
                                <Label htmlFor={`asset-${index}-serial`}>Serial/Reg Number</Label>
                                <Input
                                    id={`asset-${index}-serial`}
                                    value={asset.serialNumber}
                                    onChange={(e) => handleArrayChange('securityAssets', index, 'serialNumber', e.target.value)}
                                />
                            </div>
                            
                            <div>
                                <Label htmlFor={`asset-${index}-value`}>Estimated Asset Value</Label>
                                <Input
                                    id={`asset-${index}-value`}
                                    type="number"
                                    value={asset.estimatedValue}
                                    onChange={(e) => handleArrayChange('securityAssets', index, 'estimatedValue', e.target.value)}
                                />
                            </div>
                        </div>
                    ))}
                </Card>

                <div className="flex justify-between pt-4">
                    <Button
                        type="button"
                        variant="outline"
                        onClick={onBack}
                        disabled={loading}
                        className="flex items-center gap-2"
                    >
                        <ChevronLeft className="h-4 w-4" />
                        Back
                    </Button>
                    
                    <Button
                        type="submit"
                        disabled={loading}
                        className="bg-emerald-600 hover:bg-emerald-700 px-8"
                    >
                        {loading ? 'Submitting...' : 'Submit Application'}
                    </Button>
                </div>
            </form>
        </div>
    );
};

export default SMEBusinessForm;