import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Building, Building2, User, MoreHorizontal, ChevronLeft, ChevronRight } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface EmployerSelectionProps {
    data: any;
    onNext: (data: any) => void;
    onBack: () => void;
    loading?: boolean;
}

const employerOptions = [
    { 
        id: 'goz-ssb', 
        name: 'GOZ (Government of Zimbabwe) - SSB', 
        icon: Building,
        isSpecial: false
    },
    { 
        id: 'goz-zappa', 
        name: 'GOZ - ZAPPA', 
        icon: Building,
        isSpecial: false
    },
    { 
        id: 'goz-pension', 
        name: 'GOZ - Pension', 
        icon: Building,
        isSpecial: false
    },
    { 
        id: 'town-council', 
        name: 'Town Council', 
        icon: Building2,
        isSpecial: false
    },
    { 
        id: 'parastatal', 
        name: 'Parastatal', 
        icon: Building2,
        isSpecial: true,
        description: 'Opens searchable list'
    },
    { 
        id: 'mission-private-schools', 
        name: 'Mission and Private Schools', 
        icon: Building2,
        isSpecial: false
    },
    { 
        id: 'entrepreneur', 
        name: 'I am an Entrepreneur', 
        icon: User,
        isSpecial: false
    },
    { 
        id: 'large-corporate', 
        name: 'Large Corporate', 
        icon: Building2,
        isSpecial: true,
        description: 'Opens searchable list'
    },
    { 
        id: 'other', 
        name: 'Other', 
        icon: MoreHorizontal,
        isSpecial: false
    }
];

const EmployerSelection: React.FC<EmployerSelectionProps> = ({ data, onNext, onBack, loading }) => {
    const [showModal, setShowModal] = useState(false);
    const [modalType, setModalType] = useState<'parastatal' | 'corporate' | null>(null);
    const [selectedEmployer, setSelectedEmployer] = useState<string>('');
    
    const handleEmployerSelect = (employerId: string) => {
        const employer = employerOptions.find(e => e.id === employerId);
        
        if (employer?.isSpecial) {
            setModalType(employerId === 'parastatal' ? 'parastatal' : 'corporate');
            setShowModal(true);
        } else {
            onNext({
                employer: employerId,
                employerName: employer?.name,
                employerCategory: employer?.name // Add this field for validation
            });
        }
    };
    
    const handleModalSelect = (specificEmployer: string) => {
        onNext({ 
            employer: modalType,
            employerName: specificEmployer,
            specificEmployer
        });
    };
    
    return (
        <div className="space-y-6">
            <div className="text-center">
                <h2 className="text-2xl font-semibold mb-2">Who is your employer?</h2>
                <p className="text-gray-600 dark:text-gray-400">
                    Select your employer type from the options below
                </p>
            </div>
            
            <div className="grid gap-3 sm:grid-cols-2 lg:grid-cols-3">
                {employerOptions.map((employer) => {
                    const Icon = employer.icon;
                    return (
                        <button
                            key={employer.id}
                            onClick={() => !loading && handleEmployerSelect(employer.id)}
                            className="group p-4 text-left rounded-lg border border-[#e3e3e0] transition-all hover:border-emerald-600 hover:bg-emerald-50 hover:shadow-lg dark:border-[#3E3E3A] dark:hover:border-emerald-500 dark:hover:bg-emerald-950/20"
                        >
                            <div className="flex items-start space-x-3">
                                <Icon className="h-6 w-6 text-emerald-600 flex-shrink-0 mt-1" />
                                <div className="flex-1 min-w-0">
                                    <h3 className="text-sm font-medium mb-1 group-hover:text-emerald-600 leading-tight">
                                        {employer.name}
                                    </h3>
                                    {employer.description && (
                                        <p className="text-xs text-gray-500 dark:text-gray-400">
                                            {employer.description}
                                        </p>
                                    )}
                                </div>
                                <ChevronRight className="h-4 w-4 text-gray-400 flex-shrink-0 group-hover:text-emerald-600" />
                            </div>
                        </button>
                    );
                })}
            </div>
            
            <div className="flex justify-between pt-4">
                <Button
                    variant="outline"
                    onClick={onBack}
                    disabled={loading}
                    className="flex items-center gap-2"
                >
                    <ChevronLeft className="h-4 w-4" />
                    Back
                </Button>
            </div>
        </div>
    );
};

export default EmployerSelection;