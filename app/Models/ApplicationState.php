<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ApplicationState extends Model
{
    use SoftDeletes;
    protected $fillable = [
        'session_id',
        'channel',
        'user_identifier',
        'current_step',
        'form_data',
        'metadata',
        'expires_at',
        'reference_code',
        'reference_code_expires_at',
        'agent_id',
    ];
    
    protected $casts = [
        'form_data' => 'array',
        'metadata' => 'array',
        'expires_at' => 'datetime',
        'reference_code_expires_at' => 'datetime',
    ];
    
    public function transitions(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(StateTransition::class, 'state_id');
    }

    /**
     * Get the agent who referred this application
     */
    public function agent(): BelongsTo
    {
        return $this->belongsTo(Agent::class);
    }

    /**
     * Get commissions for this application
     */
    public function commissions(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(Commission::class, 'application_id');
    }
}
