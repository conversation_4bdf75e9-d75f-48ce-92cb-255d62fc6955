<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\DocumentUploadController;
use App\Http\Controllers\Api\ReferenceCodeController;
use App\Http\Controllers\Api\StateController;
use App\Http\Middleware\PDFErrorHandlingMiddleware;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Document upload routes with PDF error handling middleware
Route::middleware([PDFErrorHandlingMiddleware::class])->group(function () {
    Route::post('/documents/upload', [DocumentUploadController::class, 'upload']);
    Route::post('/documents/validate', [DocumentUploadController::class, 'validate']);
    Route::post('/documents/batch-upload', [DocumentUploadController::class, 'batchUpload']);
    Route::post('/documents/delete', [DocumentUploadController::class, 'delete']);
    Route::post('/documents/metadata', [DocumentUploadController::class, 'getMetadata']);
    Route::post('/documents/verify-integrity', [DocumentUploadController::class, 'verifyIntegrity']);
});

// Reference code routes with PDF error handling middleware
Route::middleware([PDFErrorHandlingMiddleware::class])->group(function () {
    Route::post('/reference-code/validate', [ReferenceCodeController::class, 'validate']);
    Route::post('/reference-code/state', [ReferenceCodeController::class, 'getState']);
    Route::post('/reference-code/generate', [ReferenceCodeController::class, 'generate']);
});

// State management routes
Route::post('/states/save', [StateController::class, 'saveState']);
Route::post('/states/retrieve', [StateController::class, 'retrieveState']);
Route::post('/states/create-application', [StateController::class, 'createApplication']);
Route::post('/states/link-sessions', [StateController::class, 'linkSessions']);

// Application status routes
Route::get('/application/status/{reference}', [App\Http\Controllers\ApplicationStatusController::class, 'getStatus']);
Route::get('/application/progress/{reference}', [App\Http\Controllers\ApplicationStatusController::class, 'getProgressDetails']);
Route::get('/application/status-updates/{reference}', [App\Http\Controllers\ApplicationStatusController::class, 'getStatusUpdates']);
Route::get('/application/insights/{reference}', [App\Http\Controllers\ApplicationStatusController::class, 'getApplicationInsights']);
Route::post('/application/status/{sessionId}', [App\Http\Controllers\ApplicationStatusController::class, 'updateStatus'])->middleware('auth:sanctum');
Route::post('/application/notifications/{reference}/mark-read', [App\Http\Controllers\ApplicationStatusController::class, 'markNotificationsAsRead']);

// WhatsApp webhook routes (no authentication required for Twilio webhooks)
Route::post('/whatsapp/webhook', [App\Http\Controllers\WhatsAppWebhookController::class, 'handleWebhook']);
Route::post('/whatsapp/status', [App\Http\Controllers\WhatsAppWebhookController::class, 'handleStatusUpdate']);

// System monitoring routes (require authentication)
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/monitoring/health', [App\Http\Controllers\MonitoringController::class, 'getSystemHealth']);
    Route::get('/monitoring/analytics', [App\Http\Controllers\MonitoringController::class, 'getUsageAnalytics']);
    Route::get('/monitoring/alerts', [App\Http\Controllers\MonitoringController::class, 'getRecentAlerts']);
    Route::get('/monitoring/dashboard', [App\Http\Controllers\MonitoringController::class, 'getDashboardData']);
    Route::post('/monitoring/health-check', [App\Http\Controllers\MonitoringController::class, 'triggerHealthCheck']);
    Route::post('/monitoring/cleanup', [App\Http\Controllers\MonitoringController::class, 'cleanupOldData']);
});