<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ApplicationState extends Model
{
    protected $fillable = [
        'session_id',
        'channel',
        'user_identifier',
        'current_step',
        'form_data',
        'metadata',
        'expires_at',
        'reference_code',
        'reference_code_expires_at',
    ];
    
    protected $casts = [
        'form_data' => 'array',
        'metadata' => 'array',
        'expires_at' => 'datetime',
    ];
    
    public function transitions(): HasMany
    {
        return $this->hasMany(StateTransition::class, 'state_id');
    }
}
