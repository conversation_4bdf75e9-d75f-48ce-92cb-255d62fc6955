import { Head } from '@inertiajs/react';
import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Search, CheckCircle, Clock, XCircle, AlertCircle, Package, FileText, ArrowLeft, Bell, RefreshCw, Eye, Download, Calendar, User, DollarSign, Building, Truck, Phone, Mail } from 'lucide-react';
import { Link } from '@inertiajs/react';

interface ApplicationDetails {
    sessionId: string;
    status: 'pending' | 'under_review' | 'approved' | 'rejected' | 'completed';
    applicantName: string;
    business: string;
    loanAmount: string;
    submittedAt: string;
    lastUpdated: string;
    timeline: TimelineEvent[];
    progressPercentage: number;
    estimatedCompletionDate?: string;
    nextAction?: string;
    rejectionReason?: string;
    approvalDetails?: {
        approvedAmount: string;
        approvedAt: string;
        disbursementDate?: string;
    };
    notifications?: NotificationItem[];
}

interface TimelineEvent {
    id: number;
    title: string;
    description: string;
    timestamp: string;
    status: 'completed' | 'current' | 'pending';
    details?: string;
    actionRequired?: boolean;
}

interface NotificationItem {
    id: string;
    type: 'info' | 'success' | 'warning' | 'error';
    title: string;
    message: string;
    timestamp: string;
    read: boolean;
}

export default function ApplicationStatus() {
    const [searchQuery, setSearchQuery] = useState('');
    const [searching, setSearching] = useState(false);
    const [applicationDetails, setApplicationDetails] = useState<ApplicationDetails | null>(null);
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [autoRefresh, setAutoRefresh] = useState(false);
    const [lastRefresh, setLastRefresh] = useState<Date | null>(null);
    const [insights, setInsights] = useState<any>(null);
    const [showInsights, setShowInsights] = useState(false);
    const [realTimeConnection, setRealTimeConnection] = useState<EventSource | null>(null);
    const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected');
    
    // Check for success redirect from application submission
    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const ref = urlParams.get('ref');
        const success = urlParams.get('success');
        
        if (ref) {
            setSearchQuery(ref);
            if (success === '1') {
                setSuccessMessage(`🎉 Application submitted successfully! Your reference number is: ${ref}`);
                // Auto-search for the application
                setTimeout(() => {
                    handleSearchWithRef(ref);
                }, 1000);
            }
        }
    }, []);

    // Auto-refresh functionality
    useEffect(() => {
        let interval: NodeJS.Timeout;
        
        if (autoRefresh && applicationDetails && ['pending', 'under_review'].includes(applicationDetails.status)) {
            interval = setInterval(() => {
                handleSearchWithRef(searchQuery);
                setLastRefresh(new Date());
            }, 30000); // Refresh every 30 seconds
        }
        
        return () => {
            if (interval) {
                clearInterval(interval);
            }
        };
    }, [autoRefresh, applicationDetails, searchQuery]);

    // Real-time updates using Server-Sent Events
    useEffect(() => {
        if (applicationDetails && ['pending', 'under_review', 'approved'].includes(applicationDetails.status)) {
            const connectToRealTimeUpdates = () => {
                setConnectionStatus('connecting');
                const eventSource = new EventSource(`/api/application/status-updates/${searchQuery}`);
                
                eventSource.onopen = () => {
                    setConnectionStatus('connected');
                    setRealTimeConnection(eventSource);
                };
                
                eventSource.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        
                        if (data.type === 'status_update') {
                            // Refresh application details when status updates
                            handleSearchWithRef(searchQuery);
                            setLastRefresh(new Date());
                        } else if (data.type === 'heartbeat') {
                            // Keep connection alive
                            setLastRefresh(new Date());
                        }
                    } catch (error) {
                        console.error('Error parsing SSE data:', error);
                    }
                };
                
                eventSource.onerror = () => {
                    setConnectionStatus('disconnected');
                    eventSource.close();
                    
                    // Retry connection after 5 seconds
                    setTimeout(connectToRealTimeUpdates, 5000);
                };
                
                return eventSource;
            };
            
            const eventSource = connectToRealTimeUpdates();
            
            return () => {
                if (eventSource) {
                    eventSource.close();
                    setRealTimeConnection(null);
                    setConnectionStatus('disconnected');
                }
            };
        }
    }, [applicationDetails?.status, searchQuery]);

    const handleSearchWithRef = async (reference: string) => {
        setSearching(true);
        setError('');
        setSuccessMessage('');
        
        try {
            // First try to validate the reference code if it's a 6-character code
            if (reference.length === 6 && /^[A-Za-z0-9]{6}$/.test(reference)) {
                // It's a reference code, validate it first
                const validateResponse = await fetch('/api/reference-code/validate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                    },
                    body: JSON.stringify({ code: reference.toUpperCase() }),
                });
                
                const validateData = await validateResponse.json();
                
                if (!validateData.success) {
                    setError('Invalid reference code. Please check and try again.');
                    setApplicationDetails(null);
                    setSearching(false);
                    return;
                }
            }
            
            // Now get the application status
            const response = await fetch(`/api/application/status/${reference}`);
            if (response.ok) {
                const data = await response.json();
                
                // Calculate progress percentage based on status and timeline
                const progressPercentage = calculateProgressPercentage(data.status, data.timeline);
                
                // Add estimated completion date and next action
                const enhancedData = {
                    ...data,
                    progressPercentage,
                    estimatedCompletionDate: getEstimatedCompletionDate(data.status),
                    nextAction: getNextAction(data.status),
                    notifications: generateNotifications(data)
                };
                
                setApplicationDetails(enhancedData);
                
                // Fetch insights for completed applications
                if (['under_review', 'approved', 'rejected', 'completed'].includes(data.status)) {
                    await fetchInsights(reference);
                }
            } else if (response.status === 404) {
                setError('Application not found. Please check your reference number.');
                setApplicationDetails(null);
            } else {
                setError('An error occurred. Please try again.');
                setApplicationDetails(null);
            }
        } catch (err) {
            setError('Failed to fetch application status. Please try again.');
            setApplicationDetails(null);
        } finally {
            setSearching(false);
        }
    };
    
    const handleSearch = async () => {
        if (!searchQuery.trim()) {
            setError('Please enter a reference number');
            return;
        }
        
        await handleSearchWithRef(searchQuery);
    };

    const calculateProgressPercentage = (status: string, timeline: TimelineEvent[]): number => {
        const completedSteps = timeline.filter(event => event.status === 'completed').length;
        const totalSteps = timeline.length;
        
        switch (status) {
            case 'pending':
                return Math.max(20, (completedSteps / totalSteps) * 100);
            case 'under_review':
                return Math.max(40, (completedSteps / totalSteps) * 100);
            case 'approved':
                return Math.max(80, (completedSteps / totalSteps) * 100);
            case 'completed':
                return 100;
            case 'rejected':
                return (completedSteps / totalSteps) * 100;
            default:
                return 0;
        }
    };

    const getEstimatedCompletionDate = (status: string): string | undefined => {
        const now = new Date();
        switch (status) {
            case 'pending':
                return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString();
            case 'under_review':
                return new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000).toLocaleDateString();
            case 'approved':
                return new Date(now.getTime() + 1 * 24 * 60 * 60 * 1000).toLocaleDateString();
            default:
                return undefined;
        }
    };

    const getNextAction = (status: string): string | undefined => {
        switch (status) {
            case 'pending':
                return 'Your application is in queue for review';
            case 'under_review':
                return 'Our team is currently reviewing your application';
            case 'approved':
                return 'Prepare for loan disbursement';
            case 'rejected':
                return 'You may submit a new application';
            case 'completed':
                return 'Track your product delivery';
            default:
                return undefined;
        }
    };

    const generateNotifications = (data: any): NotificationItem[] => {
        const notifications: NotificationItem[] = [];
        
        // Add status-based notifications
        if (data.status === 'approved') {
            notifications.push({
                id: 'approval',
                type: 'success',
                title: 'Application Approved!',
                message: `Your loan application has been approved for $${data.approvalDetails?.approvedAmount || data.loanAmount}`,
                timestamp: data.approvalDetails?.approvedAt || data.lastUpdated,
                read: false
            });
        }
        
        if (data.status === 'under_review') {
            notifications.push({
                id: 'review',
                type: 'info',
                title: 'Application Under Review',
                message: 'Our team is currently reviewing your application. We may contact you if additional information is needed.',
                timestamp: data.lastUpdated,
                read: false
            });
        }
        
        return notifications;
    };

    const fetchInsights = async (reference: string) => {
        try {
            const response = await fetch(`/api/application/insights/${reference}`);
            if (response.ok) {
                const data = await response.json();
                setInsights(data);
            }
        } catch (error) {
            console.error('Failed to fetch insights:', error);
        }
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'pending':
                return (
                    <div className="flex items-center gap-2 text-yellow-600 dark:text-yellow-400">
                        <Clock className="h-5 w-5" />
                        <span className="font-medium">Pending Review</span>
                    </div>
                );
            case 'under_review':
                return (
                    <div className="flex items-center gap-2 text-blue-600 dark:text-blue-400">
                        <AlertCircle className="h-5 w-5" />
                        <span className="font-medium">Under Review</span>
                    </div>
                );
            case 'approved':
                return (
                    <div className="flex items-center gap-2 text-green-600 dark:text-green-400">
                        <CheckCircle className="h-5 w-5" />
                        <span className="font-medium">Approved</span>
                    </div>
                );
            case 'rejected':
                return (
                    <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
                        <XCircle className="h-5 w-5" />
                        <span className="font-medium">Rejected</span>
                    </div>
                );
            case 'completed':
                return (
                    <div className="flex items-center gap-2 text-emerald-600 dark:text-emerald-400">
                        <Package className="h-5 w-5" />
                        <span className="font-medium">Completed</span>
                    </div>
                );
            default:
                return null;
        }
    };

    const getTimelineIcon = (status: string) => {
        switch (status) {
            case 'completed':
                return <CheckCircle className="h-5 w-5 text-emerald-600" />;
            case 'current':
                return <Clock className="h-5 w-5 text-blue-600 animate-pulse" />;
            case 'pending':
                return <div className="h-5 w-5 rounded-full border-2 border-gray-300" />;
            default:
                return null;
        }
    };

    const markNotificationAsRead = async (notificationId: string) => {
        try {
            const response = await fetch(`/api/application/notifications/${searchQuery}/mark-read`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                body: JSON.stringify({ notification_ids: [notificationId] }),
            });

            if (response.ok) {
                // Refresh the application details to show updated notification status
                await handleSearchWithRef(searchQuery);
            }
        } catch (error) {
            console.error('Failed to mark notification as read:', error);
        }
    };

    const markAllNotificationsAsRead = async () => {
        if (!applicationDetails?.notifications) return;
        
        try {
            const unreadNotificationIds = applicationDetails.notifications
                .filter(n => !n.read)
                .map(n => n.id);

            if (unreadNotificationIds.length === 0) return;

            const response = await fetch(`/api/application/notifications/${searchQuery}/mark-read`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                body: JSON.stringify({ notification_ids: unreadNotificationIds }),
            });

            if (response.ok) {
                // Refresh the application details to show updated notification status
                await handleSearchWithRef(searchQuery);
            }
        } catch (error) {
            console.error('Failed to mark all notifications as read:', error);
        }
    };

    return (
        <>
            <Head title="Application Status" />
            <div className="min-h-screen bg-[#FDFDFC] dark:bg-[#0a0a0a]">
                <div className="max-w-4xl mx-auto px-4 py-8">
                    {/* Header */}
                    <div className="mb-8">
                        <Link 
                            href="/"
                            className="inline-flex items-center gap-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 mb-4"
                        >
                            <ArrowLeft className="h-4 w-4" />
                            Back to Home
                        </Link>
                        <h1 className="text-3xl font-semibold text-gray-900 dark:text-gray-100">
                            Check Application Status
                        </h1>
                        <p className="text-gray-600 dark:text-gray-400 mt-2">
                            Enter your application reference number to check the current status
                        </p>
                    </div>

                    {/* Search Section */}
                    <Card className="p-6 mb-8">
                        <div className="max-w-xl mx-auto">
                            <Label htmlFor="reference" className="text-lg mb-2 block">
                                Application Reference Number
                            </Label>
                            <div className="flex gap-3">
                                <Input
                                    id="reference"
                                    placeholder="e.g., ZB2025000123 or ABC123"
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                                    className="text-lg"
                                />
                                <Button 
                                    onClick={handleSearch}
                                    disabled={searching}
                                    size="lg"
                                    className="bg-emerald-600 hover:bg-emerald-700"
                                >
                                    <Search className="h-5 w-5 mr-2" />
                                    {searching ? 'Searching...' : 'Search'}
                                </Button>
                            </div>
                            {error && (
                                <p className="text-red-600 dark:text-red-400 mt-2 text-sm">
                                    {error}
                                </p>
                            )}
                            {successMessage && (
                                <p className="text-green-600 dark:text-green-400 mt-2 text-sm font-medium">
                                    {successMessage}
                                </p>
                            )}
                        </div>
                    </Card>

                    {/* Results Section */}
                    {applicationDetails && (
                        <div className="space-y-6">
                            {/* Status Overview */}
                            <Card className="p-6">
                                <div className="flex items-center justify-between mb-6">
                                    <h2 className="text-xl font-semibold">Application Overview</h2>
                                    <div className="flex items-center gap-3">
                                        {getStatusBadge(applicationDetails.status)}
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handleSearchWithRef(searchQuery)}
                                            disabled={searching}
                                        >
                                            <RefreshCw className={`h-4 w-4 mr-2 ${searching ? 'animate-spin' : ''}`} />
                                            Refresh
                                        </Button>
                                    </div>
                                </div>

                                {/* Progress Bar */}
                                <div className="mb-6">
                                    <div className="flex items-center justify-between mb-2">
                                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Application Progress
                                        </span>
                                        <span className="text-sm text-gray-500 dark:text-gray-400">
                                            {applicationDetails.progressPercentage}%
                                        </span>
                                    </div>
                                    <Progress value={applicationDetails.progressPercentage} className="h-2" />
                                    {applicationDetails.nextAction && (
                                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                                            {applicationDetails.nextAction}
                                        </p>
                                    )}
                                    {applicationDetails.estimatedCompletionDate && ['pending', 'under_review', 'approved'].includes(applicationDetails.status) && (
                                        <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                                            Estimated completion: {applicationDetails.estimatedCompletionDate}
                                        </p>
                                    )}
                                </div>

                                {/* Auto-refresh toggle */}
                                {['pending', 'under_review'].includes(applicationDetails.status) && (
                                    <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                                        <div className="flex items-center justify-between">
                                            <div>
                                                <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                                                    Auto-refresh Status
                                                </p>
                                                <p className="text-xs text-blue-600 dark:text-blue-300">
                                                    Automatically check for updates every 30 seconds
                                                </p>
                                            </div>
                                            <Button
                                                variant={autoRefresh ? "default" : "outline"}
                                                size="sm"
                                                onClick={() => setAutoRefresh(!autoRefresh)}
                                            >
                                                <Bell className="h-4 w-4 mr-2" />
                                                {autoRefresh ? 'On' : 'Off'}
                                            </Button>
                                        </div>
                                        {lastRefresh && autoRefresh && (
                                            <p className="text-xs text-blue-500 dark:text-blue-400 mt-2">
                                                Last updated: {lastRefresh.toLocaleTimeString()}
                                            </p>
                                        )}
                                    </div>
                                )}
                                
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <p className="text-sm text-gray-500 dark:text-gray-400">Applicant Name</p>
                                        <p className="font-medium">{applicationDetails.applicantName}</p>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-500 dark:text-gray-400">Reference Number</p>
                                        <p className="font-medium">{applicationDetails.sessionId}</p>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-500 dark:text-gray-400">Product/Business</p>
                                        <p className="font-medium">{applicationDetails.business}</p>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-500 dark:text-gray-400">Loan Amount</p>
                                        <p className="font-medium">${applicationDetails.loanAmount}</p>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-500 dark:text-gray-400">Submitted Date</p>
                                        <p className="font-medium">{applicationDetails.submittedAt}</p>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-500 dark:text-gray-400">Last Updated</p>
                                        <p className="font-medium">{applicationDetails.lastUpdated}</p>
                                    </div>
                                </div>

                                {/* Rejection Reason */}
                                {applicationDetails.status === 'rejected' && applicationDetails.rejectionReason && (
                                    <div className="mt-4 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                                        <p className="text-sm font-medium text-red-800 dark:text-red-200 mb-1">
                                            Rejection Reason:
                                        </p>
                                        <p className="text-sm text-red-700 dark:text-red-300">
                                            {applicationDetails.rejectionReason}
                                        </p>
                                    </div>
                                )}

                                {/* Approval Details */}
                                {applicationDetails.status === 'approved' && applicationDetails.approvalDetails && (
                                    <div className="mt-4 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                                        <p className="text-sm font-medium text-green-800 dark:text-green-200 mb-2">
                                            Approval Details:
                                        </p>
                                        <div className="grid grid-cols-2 gap-3 text-sm">
                                            <div>
                                                <p className="text-green-700 dark:text-green-300">Approved Amount:</p>
                                                <p className="font-medium text-green-800 dark:text-green-200">
                                                    ${applicationDetails.approvalDetails.approvedAmount}
                                                </p>
                                            </div>
                                            <div>
                                                <p className="text-green-700 dark:text-green-300">Approved Date:</p>
                                                <p className="font-medium text-green-800 dark:text-green-200">
                                                    {applicationDetails.approvalDetails.approvedAt}
                                                </p>
                                            </div>
                                            {applicationDetails.approvalDetails.disbursementDate && (
                                                <div className="col-span-2">
                                                    <p className="text-green-700 dark:text-green-300">Expected Disbursement:</p>
                                                    <p className="font-medium text-green-800 dark:text-green-200">
                                                        {applicationDetails.approvalDetails.disbursementDate}
                                                    </p>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </Card>

                            {/* Notifications */}
                            {applicationDetails.notifications && applicationDetails.notifications.length > 0 && (
                                <Card className="p-6">
                                    <div className="flex items-center justify-between mb-4">
                                        <h2 className="text-xl font-semibold">Recent Updates</h2>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => markAllNotificationsAsRead()}
                                            className="text-sm"
                                        >
                                            <Bell className="h-4 w-4 mr-2" />
                                            Mark All Read
                                        </Button>
                                    </div>
                                    <div className="space-y-3">
                                        {applicationDetails.notifications.map((notification) => (
                                            <div
                                                key={notification.id}
                                                className={`p-4 rounded-lg border-l-4 transition-all hover:shadow-sm ${
                                                    notification.type === 'success' ? 'bg-green-50 dark:bg-green-900/20 border-green-500' :
                                                    notification.type === 'info' ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-500' :
                                                    notification.type === 'warning' ? 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-500' :
                                                    'bg-red-50 dark:bg-red-900/20 border-red-500'
                                                } ${!notification.read ? 'ring-2 ring-blue-200 dark:ring-blue-800' : ''}`}
                                            >
                                                <div className="flex items-start justify-between">
                                                    <div className="flex-1">
                                                        <div className="flex items-center gap-2 mb-1">
                                                            <h3 className={`font-medium ${
                                                                notification.type === 'success' ? 'text-green-800 dark:text-green-200' :
                                                                notification.type === 'info' ? 'text-blue-800 dark:text-blue-200' :
                                                                notification.type === 'warning' ? 'text-yellow-800 dark:text-yellow-200' :
                                                                'text-red-800 dark:text-red-200'
                                                            }`}>
                                                                {notification.title}
                                                            </h3>
                                                            {notification.priority === 'high' && (
                                                                <Badge variant="destructive" className="text-xs">
                                                                    High Priority
                                                                </Badge>
                                                            )}
                                                        </div>
                                                        <p className={`text-sm mt-1 ${
                                                            notification.type === 'success' ? 'text-green-700 dark:text-green-300' :
                                                            notification.type === 'info' ? 'text-blue-700 dark:text-blue-300' :
                                                            notification.type === 'warning' ? 'text-yellow-700 dark:text-yellow-300' :
                                                            'text-red-700 dark:text-red-300'
                                                        }`}>
                                                            {notification.message}
                                                        </p>
                                                        
                                                        {/* Notification Actions */}
                                                        {notification.actions && notification.actions.length > 0 && (
                                                            <div className="flex gap-2 mt-3">
                                                                {notification.actions.map((action, index) => (
                                                                    <Button
                                                                        key={index}
                                                                        variant={index === 0 ? "default" : "outline"}
                                                                        size="sm"
                                                                        onClick={() => window.location.href = action.url}
                                                                        className="text-xs"
                                                                    >
                                                                        {action.label}
                                                                    </Button>
                                                                ))}
                                                            </div>
                                                        )}
                                                        
                                                        <div className="flex items-center justify-between mt-2">
                                                            <p className="text-xs text-gray-500 dark:text-gray-400">
                                                                {new Date(notification.timestamp).toLocaleString()}
                                                            </p>
                                                            {!notification.read && (
                                                                <Button
                                                                    variant="ghost"
                                                                    size="sm"
                                                                    onClick={() => markNotificationAsRead(notification.id)}
                                                                    className="text-xs h-6 px-2"
                                                                >
                                                                    Mark as Read
                                                                </Button>
                                                            )}
                                                        </div>
                                                    </div>
                                                    {!notification.read && (
                                                        <div className="ml-2 flex flex-col items-center">
                                                            <Badge variant="secondary" className="mb-2">
                                                                New
                                                            </Badge>
                                                            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </Card>
                            )}

                            {/* Detailed Progress Section */}
                            <Card className="p-6">
                                <h2 className="text-xl font-semibold mb-6">Detailed Progress</h2>
                                
                                {/* Current Stage */}
                                <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                                    <div className="flex items-center gap-3 mb-2">
                                        <div className="p-2 bg-blue-100 dark:bg-blue-800 rounded-full">
                                            <AlertCircle className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                                        </div>
                                        <div>
                                            <h3 className="font-semibold text-blue-900 dark:text-blue-100">
                                                Current Stage: {getStatusBadge(applicationDetails.status)}
                                            </h3>
                                            <p className="text-sm text-blue-700 dark:text-blue-300">
                                                {applicationDetails.nextAction}
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                {/* Progress Milestones */}
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                                    <div className="space-y-3">
                                        <h4 className="font-medium text-gray-900 dark:text-gray-100">Completed Steps</h4>
                                        {applicationDetails.timeline
                                            .filter(event => event.status === 'completed')
                                            .map(event => (
                                                <div key={event.id} className="flex items-center gap-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                                                    <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
                                                    <div className="flex-1">
                                                        <p className="font-medium text-green-800 dark:text-green-200">{event.title}</p>
                                                        <p className="text-sm text-green-600 dark:text-green-400">{event.timestamp}</p>
                                                    </div>
                                                </div>
                                            ))}
                                    </div>
                                    
                                    <div className="space-y-3">
                                        <h4 className="font-medium text-gray-900 dark:text-gray-100">Upcoming Steps</h4>
                                        {applicationDetails.timeline
                                            .filter(event => event.status === 'pending' || event.status === 'current')
                                            .map(event => (
                                                <div key={event.id} className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                                                    {event.status === 'current' ? (
                                                        <Clock className="h-5 w-5 text-blue-600 animate-pulse flex-shrink-0" />
                                                    ) : (
                                                        <div className="h-5 w-5 rounded-full border-2 border-gray-300 flex-shrink-0" />
                                                    )}
                                                    <div className="flex-1">
                                                        <p className="font-medium text-gray-800 dark:text-gray-200">{event.title}</p>
                                                        <p className="text-sm text-gray-600 dark:text-gray-400">
                                                            {event.status === 'current' ? 'In Progress' : 'Pending'}
                                                        </p>
                                                    </div>
                                                </div>
                                            ))}
                                    </div>
                                </div>

                                {/* Estimated Time */}
                                {applicationDetails.estimatedCompletionDate && ['pending', 'under_review', 'approved'].includes(applicationDetails.status) && (
                                    <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                                        <div className="flex items-center gap-3">
                                            <Calendar className="h-5 w-5 text-yellow-600" />
                                            <div>
                                                <p className="font-medium text-yellow-800 dark:text-yellow-200">
                                                    Estimated Completion
                                                </p>
                                                <p className="text-sm text-yellow-700 dark:text-yellow-300">
                                                    {applicationDetails.estimatedCompletionDate}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </Card>

                            {/* Timeline */}
                            <Card className="p-6">
                                <h2 className="text-xl font-semibold mb-6">Application Timeline</h2>
                                <div className="space-y-4">
                                    {applicationDetails.timeline.map((event, index) => (
                                        <div key={event.id} className="flex gap-4">
                                            <div className="flex flex-col items-center">
                                                {getTimelineIcon(event.status)}
                                                {index < applicationDetails.timeline.length - 1 && (
                                                    <div className={`w-0.5 h-16 mt-2 ${
                                                        event.status === 'completed' 
                                                            ? 'bg-emerald-600' 
                                                            : 'bg-gray-300 dark:bg-gray-700'
                                                    }`} />
                                                )}
                                            </div>
                                            <div className="flex-1 pb-8">
                                                <div className="flex items-center justify-between mb-2">
                                                    <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                                                        {event.title}
                                                    </h3>
                                                    <span className="text-sm text-gray-500 dark:text-gray-400">
                                                        {event.timestamp}
                                                    </span>
                                                </div>
                                                <p className="text-gray-600 dark:text-gray-400 mb-2">
                                                    {event.description}
                                                </p>
                                                {event.details && (
                                                    <p className="text-sm text-gray-500 dark:text-gray-500 mb-2">
                                                        {event.details}
                                                    </p>
                                                )}
                                                {event.actionRequired && (
                                                    <div className="mt-2 p-2 bg-orange-50 dark:bg-orange-900/20 rounded border-l-4 border-orange-400">
                                                        <p className="text-sm text-orange-800 dark:text-orange-200 font-medium">
                                                            Action Required
                                                        </p>
                                                    </div>
                                                )}
                                            </div>
                                            <div className="flex-1 pb-8">
                                                <div className="flex items-center gap-2 mb-1">
                                                    <h3 className="font-medium text-gray-900 dark:text-gray-100">
                                                        {event.title}
                                                    </h3>
                                                    {event.actionRequired && (
                                                        <Badge variant="destructive" className="text-xs">
                                                            Action Required
                                                        </Badge>
                                                    )}
                                                    {event.status === 'current' && (
                                                        <Badge variant="default" className="text-xs">
                                                            In Progress
                                                        </Badge>
                                                    )}
                                                </div>
                                                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                                    {event.description}
                                                </p>
                                                {event.details && (
                                                    <p className="text-xs text-gray-500 dark:text-gray-500 mt-1 italic">
                                                        {event.details}
                                                    </p>
                                                )}
                                                <p className="text-xs text-gray-500 dark:text-gray-500 mt-2">
                                                    {event.timestamp}
                                                </p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </Card>

                            {/* Actions */}
                            <Card className="p-6">
                                <h2 className="text-xl font-semibold mb-4">Available Actions</h2>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                    {applicationDetails.status === 'approved' && (
                                        <Button 
                                            asChild
                                            className="bg-emerald-600 hover:bg-emerald-700"
                                        >
                                            <Link href={`/delivery/tracking?ref=${applicationDetails.sessionId}`}>
                                                <Package className="h-5 w-5 mr-2" />
                                                Track Delivery
                                            </Link>
                                        </Button>
                                    )}
                                    
                                    <Button 
                                        asChild
                                        variant="outline"
                                    >
                                        <Link href={`/application/view/${applicationDetails.sessionId}`} target="_blank">
                                            <Eye className="h-5 w-5 mr-2" />
                                            View PDF
                                        </Link>
                                    </Button>
                                    
                                    <Button 
                                        asChild
                                        variant="outline"
                                    >
                                        <Link href={`/application/download/${applicationDetails.sessionId}`}>
                                            <Download className="h-5 w-5 mr-2" />
                                            Download PDF
                                        </Link>
                                    </Button>

                                    {applicationDetails.status === 'rejected' && (
                                        <Button 
                                            asChild
                                            className="bg-blue-600 hover:bg-blue-700"
                                        >
                                            <Link href="/application">
                                                <FileText className="h-5 w-5 mr-2" />
                                                New Application
                                            </Link>
                                        </Button>
                                    )}

                                    {['pending', 'under_review'].includes(applicationDetails.status) && (
                                        <Button 
                                            asChild
                                            variant="outline"
                                        >
                                            <Link href={`/application/resume/${applicationDetails.sessionId}`}>
                                                <FileText className="h-5 w-5 mr-2" />
                                                Continue Application
                                            </Link>
                                        </Button>
                                    )}
                                </div>
                                
                                <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                                    <p className="text-sm text-gray-600 dark:text-gray-400">
                                        <strong>Need help?</strong> Contact our support <NAME_EMAIL> or call +1-800-BANK-HELP
                                    </p>
                                </div>
                            </Card>
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}