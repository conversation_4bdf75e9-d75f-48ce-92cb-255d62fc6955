<?php

namespace App\Filament\Widgets;

use App\Services\Monitoring\ApplicationMonitor;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class SystemHealthWidget extends Widget
{
    protected static string $view = 'filament.widgets.system-health';
    
    protected static ?int $sort = 3;
    
    protected int | string | array $columnSpan = 'full';

    public function getViewData(): array
    {
        $monitor = app(ApplicationMonitor::class);
        $healthMetrics = $monitor->collectHealthMetrics();
        
        return [
            'healthMetrics' => $healthMetrics,
            'overallStatus' => $this->calculateOverallStatus($healthMetrics),
            'alerts' => $monitor->getAlerts(),
        ];
    }

    private function calculateOverallStatus(array $metrics): string
    {
        $statuses = [];
        
        foreach ($metrics as $service => $data) {
            if (is_array($data) && isset($data['status'])) {
                $statuses[] = $data['status'];
            }
        }

        if (in_array('unhealthy', $statuses)) {
            return 'critical';
        }

        if (in_array('degraded', $statuses) || in_array('warning', $statuses)) {
            return 'warning';
        }

        return 'healthy';
    }
}
