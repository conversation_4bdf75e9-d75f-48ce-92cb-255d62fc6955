<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\StateManager;
use App\Services\Cache\ApplicationCacheManager;
use App\Http\Requests\SaveApplicationStateRequest;
use App\Repositories\ApplicationStateRepository;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;

class StateController extends Controller
{
    private StateManager $stateManager;
    protected ApplicationStateRepository $repository;
    protected ApplicationCacheManager $cacheManager;

    public function __construct(
        StateManager $stateManager,
        ApplicationStateRepository $repository,
        ApplicationCacheManager $cacheManager
    ) {
        $this->stateManager = $stateManager;
        $this->repository = $repository;
        $this->cacheManager = $cacheManager;
    }
    
    /**
     * Save application state
     */
    public function saveState(SaveApplicationStateRequest $request): JsonResponse
    {
        $validated = $request->validated();
        
        $state = $this->stateManager->saveState(
            $validated['session_id'],
            $validated['channel'],
            $validated['user_identifier'],
            $validated['current_step'],
            $validated['form_data'],
            $validated['metadata'] ?? []
        );

        // Cache the updated state
        $this->cacheManager->cacheApplicationState($state);

        return response()->json([
            'success' => true,
            'state_id' => $state->id,
            'expires_at' => $state->expires_at->toISOString(),
        ]);
    }
    
    /**
     * Retrieve application state
     */
    public function retrieveState(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'user' => 'required|string',
            'channel' => 'nullable|in:web,whatsapp,ussd,mobile_app',
        ]);

        // Create cache key
        $cacheKey = "application_state:{$validated['user']}:" . ($validated['channel'] ?? 'default');

        // Try to get from cache first
        $state = Cache::remember($cacheKey, 300, function () use ($validated) {
            return $this->stateManager->retrieveState(
                $validated['user'],
                $validated['channel'] ?? null
            );
        });

        if (!$state) {
            return response()->json([
                'success' => false,
                'message' => 'No active state found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'session_id' => $state->session_id,
            'current_step' => $state->current_step,
            'form_data' => $state->form_data,
            'can_resume' => true,
            'expires_in' => $state->expires_at->diffInSeconds(now()),
        ]);
    }
    
    /**
     * Create a new application (final submission)
     */
    public function createApplication(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'sessionId' => 'required|string',
            'data' => 'required|array',
        ]);
        
        try {
            // Update the application state to completed
            $state = $this->stateManager->saveState(
                $validated['sessionId'],
                'web', // Default to web channel
                $request->ip() ?? 'unknown',
                'completed',
                $validated['data'],
                [
                    'completed_at' => now()->toISOString(),
                    'submission_ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ]
            );
            
            return response()->json([
                'success' => true,
                'message' => 'Application submitted successfully',
                'application_id' => $state->session_id,
                'reference_number' => $state->session_id,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to submit application: ' . $e->getMessage(),
            ], 500);
        }
    }
    
    /**
     * Link sessions across channels
     */
    public function linkSessions(Request $request): JsonResponse
    {
        // This will be implemented later
        return response()->json([
            'success' => false,
            'message' => 'Feature not yet implemented',
        ], 501);
    }
}