<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\ClientErrorController;
use App\Http\Controllers\Api\DocumentUploadController;
use App\Http\Controllers\Api\ReferenceCodeController;
use App\Http\Controllers\Api\StateController;
use App\Http\Middleware\PDFErrorHandlingMiddleware;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Client error logging (higher rate limit for error reporting)
Route::middleware(['throttle:300,1'])->group(function () {
    Route::post('/client-errors', [ClientErrorController::class, 'logError']);
});

// Public API routes with rate limiting
Route::middleware(['throttle:' . env('API_RATE_LIMIT_PER_MINUTE', 60) . ',1'])->group(function () {
    // Document upload routes with PDF error handling middleware
    Route::middleware([PDFErrorHandlingMiddleware::class])->group(function () {
        Route::post('/documents/upload', [DocumentUploadController::class, 'upload']);
        Route::post('/documents/validate', [DocumentUploadController::class, 'validate']);
        Route::post('/documents/batch-upload', [DocumentUploadController::class, 'batchUpload']);
        Route::post('/documents/delete', [DocumentUploadController::class, 'delete']);
        Route::post('/documents/metadata', [DocumentUploadController::class, 'getMetadata']);
        Route::post('/documents/verify-integrity', [DocumentUploadController::class, 'verifyIntegrity']);
    });

    // Reference code routes with PDF error handling middleware
    Route::middleware([PDFErrorHandlingMiddleware::class])->group(function () {
        Route::post('/reference-code/validate', [ReferenceCodeController::class, 'validate']);
        Route::post('/reference-code/state', [ReferenceCodeController::class, 'getState']);
        Route::post('/reference-code/generate', [ReferenceCodeController::class, 'generate']);
    });

    // State management routes
    Route::post('/states/save', [StateController::class, 'saveState']);
    Route::post('/states/retrieve', [StateController::class, 'retrieveState']);
    Route::post('/states/create-application', [StateController::class, 'createApplication']);
    Route::post('/states/link-sessions', [StateController::class, 'linkSessions']);
});

// Application status routes (public with rate limiting)
Route::middleware(['throttle:' . env('API_RATE_LIMIT_PER_MINUTE', 60) . ',1'])->group(function () {
    Route::get('/application/status/{reference}', [App\Http\Controllers\ApplicationStatusController::class, 'getStatus']);
    Route::get('/application/progress/{reference}', [App\Http\Controllers\ApplicationStatusController::class, 'getProgressDetails']);
    Route::get('/application/status-updates/{reference}', [App\Http\Controllers\ApplicationStatusController::class, 'getStatusUpdates']);
    Route::get('/application/insights/{reference}', [App\Http\Controllers\ApplicationStatusController::class, 'getApplicationInsights']);
    Route::post('/application/notifications/{reference}/mark-read', [App\Http\Controllers\ApplicationStatusController::class, 'markNotificationsAsRead']);
});

// Authenticated application routes with higher rate limits
Route::middleware(['auth:sanctum', 'throttle:' . env('AUTHENTICATED_API_RATE_LIMIT_PER_MINUTE', 120) . ',1'])->group(function () {
    Route::post('/application/status/{sessionId}', [App\Http\Controllers\ApplicationStatusController::class, 'updateStatus']);
});

// WhatsApp webhook routes (special rate limiting for webhooks)
Route::middleware(['throttle:300,1'])->group(function () {
    Route::post('/whatsapp/webhook', [App\Http\Controllers\WhatsAppWebhookController::class, 'handleWebhook']);
    Route::post('/whatsapp/status', [App\Http\Controllers\WhatsAppWebhookController::class, 'handleStatusUpdate']);
});

// System monitoring routes (require authentication with higher rate limits)
Route::middleware(['auth:sanctum', 'throttle:' . env('AUTHENTICATED_API_RATE_LIMIT_PER_MINUTE', 120) . ',1'])->group(function () {
    Route::get('/monitoring/health', [App\Http\Controllers\MonitoringController::class, 'getSystemHealth']);
    Route::get('/monitoring/analytics', [App\Http\Controllers\MonitoringController::class, 'getUsageAnalytics']);
    Route::get('/monitoring/alerts', [App\Http\Controllers\MonitoringController::class, 'getRecentAlerts']);
    Route::get('/monitoring/dashboard', [App\Http\Controllers\MonitoringController::class, 'getDashboardData']);
    Route::post('/monitoring/health-check', [App\Http\Controllers\MonitoringController::class, 'triggerHealthCheck']);
    Route::post('/monitoring/cleanup', [App\Http\Controllers\MonitoringController::class, 'cleanupOldData']);
});