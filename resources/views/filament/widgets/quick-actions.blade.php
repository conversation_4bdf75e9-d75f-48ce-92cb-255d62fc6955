<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            Quick Actions
        </x-slot>
        
        <x-slot name="description">
            Common administrative tasks and system overview
        </x-slot>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            @foreach($this->getViewData()['actions'] as $action)
                <div class="relative">
                    @if(isset($action['url']))
                        <a href="{{ $action['url'] }}" 
                           class="block p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-{{ $action['color'] }}-500 hover:shadow-lg transition-all duration-200">
                    @else
                        <button wire:click="{{ $action['action'] }}" 
                                class="w-full p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-{{ $action['color'] }}-500 hover:shadow-lg transition-all duration-200 text-left">
                    @endif
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="flex-shrink-0">
                                    <x-heroicon-o-document-magnifying-glass class="w-6 h-6 text-{{ $action['color'] }}-500" />
                                </div>
                                <div>
                                    <h3 class="text-sm font-medium text-gray-900 dark:text-white">
                                        {{ $action['label'] }}
                                    </h3>
                                </div>
                            </div>
                            @if($action['badge'])
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-{{ $action['color'] }}-100 text-{{ $action['color'] }}-800 dark:bg-{{ $action['color'] }}-900 dark:text-{{ $action['color'] }}-200">
                                    {{ $action['badge'] }}
                                </span>
                            @endif
                        </div>
                    @if(isset($action['url']))
                        </a>
                    @else
                        </button>
                    @endif
                </div>
            @endforeach
        </div>

        <!-- Quick Stats Summary -->
        <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">System Overview</h4>
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                @php
                    $stats = $this->getViewData()['stats'];
                    $statItems = [
                        ['label' => 'Pending Approvals', 'value' => $stats['pending_approvals'], 'color' => 'warning'],
                        ['label' => 'Urgent Apps', 'value' => $stats['urgent_applications'], 'color' => 'danger'],
                        ['label' => 'Today\'s Apps', 'value' => $stats['today_applications'], 'color' => 'info'],
                        ['label' => 'Pending Commissions', 'value' => $stats['pending_commissions'], 'color' => 'warning'],
                        ['label' => 'Active Agents', 'value' => $stats['active_agents'], 'color' => 'success'],
                        ['label' => 'Total Products', 'value' => $stats['total_products'], 'color' => 'primary'],
                    ];
                @endphp
                
                @foreach($statItems as $stat)
                    <div class="text-center">
                        <div class="text-2xl font-bold text-{{ $stat['color'] }}-600 dark:text-{{ $stat['color'] }}-400">
                            {{ number_format($stat['value']) }}
                        </div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">
                            {{ $stat['label'] }}
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
