import { type SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import { useState } from 'react';
import { Globe, CreditCard, Briefcase, FileText, Package, ChevronRight } from 'lucide-react';

export default function Welcome() {
    const { auth } = usePage<SharedData>().props;
    const [currentStep, setCurrentStep] = useState<'language' | 'intent'>('language');
    const [selectedLanguage, setSelectedLanguage] = useState<string>('');

    const languages = [
        { code: 'en', name: 'English', greeting: 'Welcome to ZB Bank' },
        { code: 'sn', name: '<PERSON><PERSON><PERSON>', greeting: 'Mauya kuZB Bank' },
        { code: 'nd', name: '<PERSON><PERSON><PERSON><PERSON>', greeting: 'Ngiyakwemukela kuZB Bank' }
    ];

    const intents = [
        { 
            id: 'hirePurchase', 
            name: 'Apply for Hire Purchase Credit', 
            icon: CreditCard,
            description: 'Personal and Household Products',
            route: 'application.wizard'
        },
        { 
            id: 'microBiz', 
            name: 'Apply for Micro Biz', 
            icon: Briefcase,
            description: '<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Spana Starter Pack',
            route: 'application.wizard'
        },
        { 
            id: 'checkStatus', 
            name: 'Get an update on your application status', 
            icon: FileText,
            description: 'Check your existing application',
            route: 'application.status'
        },
        { 
            id: 'trackDelivery', 
            name: 'Track the delivery of product/equipment', 
            icon: Package,
            description: 'Monitor your order delivery',
            route: 'delivery.tracking'
        }
    ];

    const handleLanguageSelect = (language: string) => {
        setSelectedLanguage(language);
        setCurrentStep('intent');
    };

    const handleIntentSelect = (intent: string) => {
        const selectedIntent = intents.find(i => i.id === intent);
        if (selectedIntent) {
            if (intent === 'hirePurchase' || intent === 'microBiz') {
                window.location.href = route(selectedIntent.route, { intent, language: selectedLanguage });
            } else {
                window.location.href = route(selectedIntent.route, { language: selectedLanguage });
            }
        }
    };

    const selectedLang = languages.find(l => l.code === selectedLanguage);

    return (
        <>
            <Head title="ZB Bank Application">
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
            </Head>
            <div className="flex min-h-screen flex-col items-center bg-[#FDFDFC] p-6 text-[#1b1b18] lg:justify-center lg:p-8 dark:bg-[#0a0a0a]">
                <header className="mb-6 w-full max-w-[335px] text-sm not-has-[nav]:hidden lg:max-w-4xl">
                    <nav className="flex items-center justify-end gap-4">
                        {auth.user ? (
                            <Link
                                href={route('dashboard')}
                                className="inline-block rounded-sm border border-[#********] px-5 py-1.5 text-sm leading-normal text-[#1b1b18] hover:border-[#1915014a] dark:border-[#3E3E3A] dark:text-[#EDEDEC] dark:hover:border-[#62605b]"
                            >
                                Dashboard
                            </Link>
                        ) : (
                            <>
                                <Link
                                    href={route('login')}
                                    className="inline-block rounded-sm border border-transparent px-5 py-1.5 text-sm leading-normal text-[#1b1b18] hover:border-[#********] dark:text-[#EDEDEC] dark:hover:border-[#3E3E3A]"
                                >
                                    Log in
                                </Link>
                                <Link
                                    href={route('register')}
                                    className="inline-block rounded-sm border border-[#********] px-5 py-1.5 text-sm leading-normal text-[#1b1b18] hover:border-[#1915014a] dark:border-[#3E3E3A] dark:text-[#EDEDEC] dark:hover:border-[#62605b]"
                                >
                                    Register
                                </Link>
                            </>
                        )}
                    </nav>
                </header>
                
                <div className="flex w-full items-center justify-center opacity-100 transition-opacity duration-750 lg:grow starting:opacity-0">
                    <main className="w-full max-w-4xl">
                        <div className="bg-white rounded-lg shadow-[inset_0px_0px_0px_1px_rgba(26,26,0,0.16)] p-6 lg:p-20 dark:bg-[#161615] dark:text-[#EDEDEC] dark:shadow-[inset_0px_0px_0px_1px_#fffaed2d]">
                            
                            {currentStep === 'language' && (
                                <div className="space-y-8">
                                    <div className="text-center">
                                        <Globe className="mx-auto h-16 w-16 text-emerald-600 mb-6" />
                                        <h1 className="text-3xl font-bold mb-4">ZB Bank Application</h1>
                                        <p className="text-lg text-[#706f6c] dark:text-[#A1A09A]">
                                            Please select your preferred language to continue
                                        </p>
                                    </div>
                                    
                                    <div className="grid gap-4 sm:grid-cols-3 max-w-3xl mx-auto">
                                        {languages.map((lang) => (
                                            <button
                                                key={lang.code}
                                                onClick={() => handleLanguageSelect(lang.code)}
                                                className="group p-8 text-center rounded-lg border border-[#e3e3e0] transition-all hover:border-emerald-600 hover:bg-emerald-50 hover:shadow-lg dark:border-[#3E3E3A] dark:hover:border-emerald-500 dark:hover:bg-emerald-950/20"
                                            >
                                                <h3 className="text-xl font-semibold mb-2 group-hover:text-emerald-600">{lang.name}</h3>
                                                <p className="text-sm text-[#706f6c] dark:text-[#A1A09A]">{lang.greeting}</p>
                                                <ChevronRight className="mx-auto mt-4 h-6 w-6 text-gray-400 group-hover:text-emerald-600" />
                                            </button>
                                        ))}
                                    </div>
                                </div>
                            )}

                            {currentStep === 'intent' && (
                                <div className="space-y-8">
                                    <div className="text-center">
                                        <h1 className="text-3xl font-bold mb-4">
                                            {selectedLang?.greeting}
                                        </h1>
                                        <p className="text-lg text-[#706f6c] dark:text-[#A1A09A]">
                                            How can we help you today?
                                        </p>
                                        <button
                                            onClick={() => setCurrentStep('language')}
                                            className="mt-4 text-sm text-emerald-600 hover:text-emerald-700"
                                        >
                                            ← Change Language
                                        </button>
                                    </div>
                                    
                                    <div className="grid gap-4 sm:grid-cols-2 max-w-4xl mx-auto">
                                        {intents.map((intent) => {
                                            const Icon = intent.icon;
                                            return (
                                                <button
                                                    key={intent.id}
                                                    onClick={() => handleIntentSelect(intent.id)}
                                                    className="group p-6 text-left rounded-lg border border-[#e3e3e0] transition-all hover:border-emerald-600 hover:bg-emerald-50 hover:shadow-lg dark:border-[#3E3E3A] dark:hover:border-emerald-500 dark:hover:bg-emerald-950/20"
                                                >
                                                    <div className="flex items-start space-x-4">
                                                        <Icon className="h-8 w-8 text-emerald-600 flex-shrink-0 group-hover:scale-110 transition-transform" />
                                                        <div className="flex-1">
                                                            <h3 className="text-lg font-semibold mb-2 group-hover:text-emerald-600">
                                                                {intent.name}
                                                            </h3>
                                                            <p className="text-sm text-[#706f6c] dark:text-[#A1A09A]">
                                                                {intent.description}
                                                            </p>
                                                        </div>
                                                        <ChevronRight className="h-5 w-5 text-gray-400 flex-shrink-0 group-hover:text-emerald-600" />
                                                    </div>
                                                </button>
                                            );
                                        })}
                                    </div>
                                </div>
                            )}

                        </div>
                    </main>
                </div>
            </div>
        </>
    );
}