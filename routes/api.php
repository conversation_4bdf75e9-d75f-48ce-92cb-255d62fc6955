<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\ProductController;
use App\Http\Controllers\Api\StateController;

// Product API routes
Route::prefix('products')->group(function () {
    Route::get('/', [ProductController::class, 'index']); // Legacy endpoint
    Route::get('/frontend-catalog', [ProductController::class, 'getFrontendCatalog']); // Frontend-compatible format
    Route::get('/categories', [ProductController::class, 'getCategories']);
    Route::get('/category/{categoryId}', [ProductController::class, 'getProductsByCategory']);
    Route::get('/product/{productId}', [ProductController::class, 'getProduct']);
    Route::get('/search', [ProductController::class, 'searchProducts']);
    Route::get('/statistics', [ProductController::class, 'getStatistics']);
});

// Application State API routes
Route::prefix('states')->group(function () {
    Route::post('/save', [StateController::class, 'saveState']);
    Route::post('/retrieve', [StateController::class, 'retrieveState']);
    Route::post('/create-application', [StateController::class, 'createApplication']);
    Route::post('/link', [StateController::class, 'linkSessions']);
    Route::get('/status/{sessionId}', [StateController::class, 'getStatus']);
});

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});
