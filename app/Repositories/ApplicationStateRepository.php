<?php

namespace App\Repositories;

use App\Models\ApplicationState;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class ApplicationStateRepository
{
    /**
     * Find application state by session ID
     */
    public function findBySessionId(string $sessionId): ?ApplicationState
    {
        return ApplicationState::where('session_id', $sessionId)->first();
    }

    /**
     * Find application state by reference code
     */
    public function findByReferenceCode(string $referenceCode): ?ApplicationState
    {
        return ApplicationState::where('reference_code', $referenceCode)->first();
    }

    /**
     * Find application states by user identifier
     */
    public function findByUserIdentifier(string $userIdentifier, string $channel = null): Collection
    {
        $query = ApplicationState::where('user_identifier', $userIdentifier);
        
        if ($channel) {
            $query->where('channel', $channel);
        }
        
        return $query->orderBy('created_at', 'desc')->get();
    }

    /**
     * Create new application state
     */
    public function create(array $data): ApplicationState
    {
        return ApplicationState::create($data);
    }

    /**
     * Update application state
     */
    public function update(ApplicationState $applicationState, array $data): bool
    {
        return $applicationState->update($data);
    }

    /**
     * Delete application state (soft delete)
     */
    public function delete(ApplicationState $applicationState): bool
    {
        return $applicationState->delete();
    }

    /**
     * Get application states by current step
     */
    public function getByCurrentStep(string $step, int $limit = null): Collection
    {
        $query = ApplicationState::where('current_step', $step)
            ->orderBy('updated_at', 'desc');
            
        if ($limit) {
            $query->limit($limit);
        }
        
        return $query->get();
    }

    /**
     * Get expired application states
     */
    public function getExpired(): Collection
    {
        return ApplicationState::where('expires_at', '<', now())
            ->whereNotNull('expires_at')
            ->get();
    }

    /**
     * Get application states by channel
     */
    public function getByChannel(string $channel, int $limit = null): Collection
    {
        $query = ApplicationState::where('channel', $channel)
            ->orderBy('created_at', 'desc');
            
        if ($limit) {
            $query->limit($limit);
        }
        
        return $query->get();
    }

    /**
     * Get application states created within date range
     */
    public function getByDateRange(\DateTime $startDate, \DateTime $endDate): Collection
    {
        return ApplicationState::whereBetween('created_at', [$startDate, $endDate])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get paginated application states with filters
     */
    public function getPaginated(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = ApplicationState::query();

        // Apply filters
        if (isset($filters['channel'])) {
            $query->where('channel', $filters['channel']);
        }

        if (isset($filters['current_step'])) {
            $query->where('current_step', $filters['current_step']);
        }

        if (isset($filters['user_identifier'])) {
            $query->where('user_identifier', 'like', '%' . $filters['user_identifier'] . '%');
        }

        if (isset($filters['reference_code'])) {
            $query->where('reference_code', 'like', '%' . $filters['reference_code'] . '%');
        }

        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        // Apply search
        if (isset($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('session_id', 'like', "%{$search}%")
                  ->orWhere('user_identifier', 'like', "%{$search}%")
                  ->orWhere('reference_code', 'like', "%{$search}%")
                  ->orWhereJsonContains('form_data->formResponses->firstName', $search)
                  ->orWhereJsonContains('form_data->formResponses->lastName', $search)
                  ->orWhereJsonContains('form_data->formResponses->surname', $search);
            });
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Get statistics
     */
    public function getStatistics(): array
    {
        $total = ApplicationState::count();
        $byChannel = ApplicationState::selectRaw('channel, count(*) as count')
            ->groupBy('channel')
            ->pluck('count', 'channel')
            ->toArray();

        $byStep = ApplicationState::selectRaw('current_step, count(*) as count')
            ->groupBy('current_step')
            ->pluck('count', 'current_step')
            ->toArray();

        $recentCount = ApplicationState::where('created_at', '>=', now()->subDays(7))->count();
        $completedCount = ApplicationState::where('current_step', 'completed')->count();

        return [
            'total' => $total,
            'by_channel' => $byChannel,
            'by_step' => $byStep,
            'recent_week' => $recentCount,
            'completed' => $completedCount,
            'completion_rate' => $total > 0 ? round(($completedCount / $total) * 100, 2) : 0,
        ];
    }

    /**
     * Cleanup expired sessions
     */
    public function cleanupExpired(): int
    {
        $expired = $this->getExpired();
        $count = $expired->count();
        
        foreach ($expired as $applicationState) {
            $applicationState->delete();
        }
        
        return $count;
    }

    /**
     * Get application states that need reference codes
     */
    public function getNeedingReferenceCodes(): Collection
    {
        return ApplicationState::whereNull('reference_code')
            ->where('current_step', '!=', 'language')
            ->where('current_step', '!=', 'intent')
            ->get();
    }

    /**
     * Bulk update application states
     */
    public function bulkUpdate(array $sessionIds, array $data): int
    {
        return ApplicationState::whereIn('session_id', $sessionIds)->update($data);
    }

    /**
     * Get application states with transitions
     */
    public function getWithTransitions(string $sessionId): ?ApplicationState
    {
        return ApplicationState::with('transitions')
            ->where('session_id', $sessionId)
            ->first();
    }

    /**
     * Search application states by form data
     */
    public function searchByFormData(string $field, string $value): Collection
    {
        return ApplicationState::whereJsonContains("form_data->formResponses->{$field}", $value)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get application states by metadata
     */
    public function getByMetadata(string $key, string $value): Collection
    {
        return ApplicationState::whereJsonContains("metadata->{$key}", $value)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Count application states by criteria
     */
    public function countByCriteria(array $criteria): int
    {
        $query = ApplicationState::query();

        foreach ($criteria as $field => $value) {
            if (is_array($value)) {
                $query->whereIn($field, $value);
            } else {
                $query->where($field, $value);
            }
        }

        return $query->count();
    }
}
