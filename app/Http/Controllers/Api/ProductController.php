<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ProductController extends Controller
{
    public function index()
    {
        $categories = DB::table('product_categories')->get();

        $productCatalog = [];

        foreach ($categories as $category) {
            $subcategories = DB::table('product_sub_categories')->where('product_category_id', $category->id)->get();

            $categoryData = [
                'id' => $category->id,
                'name' => $category->name,
                'emoji' => $category->emoji,
                'subcategories' => [],
            ];

            foreach ($subcategories as $subcategory) {
                $products = DB::table('products')->where('product_sub_category_id', $subcategory->id)->get();

                $subcategoryData = [
                    'name' => $subcategory->name,
                    'businesses' => [],
                ];

                foreach ($products as $product) {
                    $packageSizes = DB::table('product_package_sizes')->where('product_id', $product->id)->get();

                    $productData = [
                        'name' => $product->name,
                        'basePrice' => (float) $product->base_price,
                        'scales' => [],
                    ];

                    foreach ($packageSizes as $packageSize) {
                        $productData['scales'][] = [
                            'name' => $packageSize->name,
                            'multiplier' => (float) $packageSize->multiplier,
                            'custom_price' => isset($packageSize->custom_price) ? (float) $packageSize->custom_price : null,
                        ];
                    }

                    $subcategoryData['businesses'][] = $productData;
                }

                $categoryData['subcategories'][] = $subcategoryData;
            }

            $productCatalog[] = $categoryData;
        }

        return response()->json($productCatalog);
    }
}