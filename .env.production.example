APP_NAME="Bancozim Loan System"
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_URL=https://your-domain.com

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=cache
APP_MAINTENANCE_STORE=redis

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=daily
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=warning

# Production Database Configuration
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=bancozim_production
DB_USERNAME=bancozim_user
DB_PASSWORD=your_secure_database_password

# Session Configuration
SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=true
SESSION_PATH=/
SESSION_DOMAIN=.your-domain.com

BROADCAST_CONNECTION=redis
FILESYSTEM_DISK=s3
QUEUE_CONNECTION=redis

# Cache Configuration
CACHE_STORE=redis
CACHE_PREFIX=bancozim_

MEMCACHED_HOST=127.0.0.1

# Redis Configuration
REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=your_redis_password
REDIS_PORT=6379

# Mail Configuration
MAIL_MAILER=smtp
MAIL_SCHEME=tls
MAIL_HOST=smtp.your-provider.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=bancozim-production
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# Twilio WhatsApp Configuration
TWILIO_ACCOUNT_SID=your_production_twilio_account_sid
TWILIO_AUTH_TOKEN=your_production_twilio_auth_token
TWILIO_WHATSAPP_FROM=whatsapp:+your_production_whatsapp_number

# PDF Security Configuration
PDF_ENCRYPTION_ENABLED=true
PDF_OWNER_PASSWORD_LENGTH=16

# File Upload Configuration
MAX_FILE_SIZE=10240
ALLOWED_FILE_TYPES=pdf,jpg,jpeg,png
VIRUS_SCANNING_ENABLED=true

# Rate Limiting Configuration
API_RATE_LIMIT_PER_MINUTE=60
AUTHENTICATED_API_RATE_LIMIT_PER_MINUTE=120

# Security Configuration
FORCE_HTTPS=true
SESSION_SECURE_COOKIE=true
CONTENT_SECURITY_POLICY_ENABLED=true

# Monitoring Configuration
TELESCOPE_ENABLED=false
APM_ENABLED=true
ERROR_REPORTING_ENABLED=true

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=30
