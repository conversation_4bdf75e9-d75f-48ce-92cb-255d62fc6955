import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { ChevronLeft, User, Building, CreditCard, Users, Smartphone } from 'lucide-react';

interface ZBAccountOpeningFormProps {
    data: any;
    onNext: (data: any) => void;
    onBack: () => void;
    loading?: boolean;
}

const ZBAccountOpeningForm: React.FC<ZBAccountOpeningFormProps> = ({ data, onNext, onBack, loading }) => {
    // Calculate credit facility details from product selection
    const calculateCreditFacilityDetails = () => {
        const businessName = data.business; // string from ProductSelection
        const finalPrice = data.amount || 0; // number from ProductSelection
        const intent = data.intent || 'hirePurchase';
        
        let facilityType = '';
        if (intent === 'hirePurchase' && businessName) {
            facilityType = `Hire Purchase Credit - ${businessName}`;
        } else if ((intent === 'microBiz' || intent === 'microBizLoan') && businessName) {
            facilityType = `Micro Biz Loan - ${businessName}`;
        } else if (businessName) {
            // Fallback if intent doesn't match
            facilityType = `Credit Facility - ${businessName}`;
        }
        
        // Calculate tenure based on amount
        let tenure = 12; // default
        if (finalPrice <= 1000) tenure = 6;
        else if (finalPrice <= 5000) tenure = 12;
        else if (finalPrice <= 15000) tenure = 18;
        else tenure = 24;
        
        // Calculate monthly payment (10% annual interest)
        const interestRate = 0.10;
        const monthlyInterestRate = interestRate / 12;
        const monthlyPayment = finalPrice > 0 ? 
            (finalPrice * monthlyInterestRate * Math.pow(1 + monthlyInterestRate, tenure)) /
            (Math.pow(1 + monthlyInterestRate, tenure) - 1) : 0;
        
        return {
            creditFacilityType: facilityType,
            loanAmount: finalPrice.toFixed(2),
            loanTenure: tenure.toString(),
            monthlyPayment: monthlyPayment.toFixed(2),
            interestRate: '10.0'
        };
    };
    
    const creditDetails = calculateCreditFacilityDetails();
    const businessName = data.business;
    
    const [formData, setFormData] = useState({
        // Credit Facility Details (pre-populated)
        ...creditDetails,
        
        // Account Specifications
        accountNumber: '',
        accountCurrency: '',
        serviceCenter: '',
        
        // Personal Details
        title: '',
        firstName: '',
        surname: '',
        maidenName: '',
        otherNames: '',
        gender: '',
        dateOfBirth: '',
        placeOfBirth: '',
        nationality: '',
        maritalStatus: '',
        citizenship: '',
        dependents: '',
        nationalIdNumber: '',
        driversLicense: '',
        passportNumber: '',
        passportExpiry: '',
        countryOfResidence: '',
        highestEducation: '',
        hobbies: '',
        
        // Contact Details
        residentialAddress: '',
        telephoneRes: '',
        mobile: '',
        bus: '',
        emailAddress: '',
        
        // Employment Details
        employerName: '',
        occupation: '',
        employmentStatus: '',
        businessDescription: '',
        employerType: {
            government: false,
            localCompany: false,
            multinational: false,
            ngo: false,
            other: false,
            otherSpecify: ''
        },
        employerAddress: '',
        employerContact: '',
        grossMonthlySalary: '',
        otherIncome: '',
        
        // Spouse/Next of Kin
        spouseTitle: '',
        spouseFirstName: '',
        spouseSurname: '',
        spouseAddress: '',
        spouseIdNumber: '',
        spouseContact: '',
        spouseRelationship: '',
        spouseGender: '',
        spouseEmail: '',
        
        // ZB Life Funeral Cash Cover
        funeralCover: {
            dependents: [],
            principalMember: {
                memorialCashBenefit: '',
                tombstoneCashBenefit: '',
                groceryBenefit: '',
                schoolFeesBenefit: '',
                personalAccidentBenefit: ''
            }
        },
        
        // Personal Accident Benefit
        personalAccidentBenefit: {
            surname: '',
            forenames: ''
        },
        
        // Other Services
        smsAlerts: false,
        smsNumber: '',
        eStatements: false,
        eStatementsEmail: '',
        
        // Digital Banking
        mobileMoneyEcocash: false,
        mobileMoneyNumber: '',
        eWallet: false,
        eWalletNumber: '',
        whatsappBanking: false,
        internetBanking: false,
        
        // Supporting Documents
        supportingDocs: {
            passportPhotos: false,
            proofOfResidence: false,
            payslip: false,
            nationalId: false,
            passport: false,
            driversLicense: false
        },
        
        // Declaration
        declaration: {
            fullName: '',
            signature: '',
            date: ''
        }
    });

    const handleInputChange = (field: string, value: string | boolean) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onNext(formData);
    };

    return (
        <div className="max-w-4xl mx-auto space-y-6">
            <div className="text-center">
                <h2 className="text-2xl font-semibold mb-2">Individual Account Opening Application</h2>
                <p className="text-gray-600 dark:text-gray-400">
                    Complete your account opening application
                </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
                {/* Credit Facility Application Details */}
                <Card className="p-6 bg-green-50 dark:bg-green-900 border-green-200 dark:border-green-700">
                    <div className="flex items-center mb-4">
                        <CreditCard className="h-6 w-6 text-green-600 mr-3" />
                        <h3 className="text-lg font-semibold text-green-800 dark:text-green-200">Credit Facility Application Details</h3>
                    </div>
                    
                    {/* Pre-populated readonly fields */}
                    <div className="grid gap-4 mb-4 p-4 bg-white dark:bg-gray-800 rounded-lg border">
                        <div className="text-sm text-green-600 dark:text-green-400 font-medium mb-2">
                            ✅ The following details have been automatically filled based on your product selection:
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <Label className="text-gray-700 dark:text-gray-300">Credit Facility Type</Label>
                                <Input
                                    value={formData.creditFacilityType}
                                    readOnly
                                    className="bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600"
                                />
                            </div>
                            <div>
                                <Label className="text-gray-700 dark:text-gray-300">Loan Amount (USD)</Label>
                                <div className="relative">
                                    <span className="absolute left-3 top-2.5 text-gray-500">$</span>
                                    <Input
                                        value={formData.loanAmount}
                                        readOnly
                                        className="pl-8 bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600"
                                    />
                                </div>
                            </div>
                            <div>
                                <Label className="text-gray-700 dark:text-gray-300">Loan Tenure (Months)</Label>
                                <Input
                                    value={`${formData.loanTenure} months`}
                                    readOnly
                                    className="bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600"
                                />
                            </div>
                            <div>
                                <Label className="text-gray-700 dark:text-gray-300">Monthly Payment (USD)</Label>
                                <div className="relative">
                                    <span className="absolute left-3 top-2.5 text-gray-500">$</span>
                                    <Input
                                        value={formData.monthlyPayment}
                                        readOnly
                                        className="pl-8 bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600"
                                    />
                                </div>
                            </div>
                            <div>
                                <Label className="text-gray-700 dark:text-gray-300">Interest Rate (%)</Label>
                                <Input
                                    value={`${formData.interestRate}%`}
                                    readOnly
                                    className="bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600"
                                />
                            </div>
                        </div>
                    </div>
                </Card>

                {/* Account Specifications */}
                <Card className="p-6">
                    <div className="flex items-center mb-4">
                        <CreditCard className="h-6 w-6 text-emerald-600 mr-3" />
                        <h3 className="text-lg font-semibold">Account Specifications</h3>
                    </div>
                    
                    <div className="grid gap-4 md:grid-cols-3">
                        <div>
                            <Label htmlFor="accountNumber">Account Number (Pre-Official Use Only)</Label>
                            <div className="flex gap-1">
                                {[...Array(4)].map((_, i) => (
                                    <Input key={i} className="w-12 text-center" maxLength={1} />
                                ))}
                                <span className="mx-2">-</span>
                                {[...Array(6)].map((_, i) => (
                                    <Input key={i + 4} className="w-12 text-center" maxLength={1} />
                                ))}
                                <span className="mx-2">-</span>
                                {[...Array(2)].map((_, i) => (
                                    <Input key={i + 10} className="w-12 text-center" maxLength={1} />
                                ))}
                            </div>
                        </div>
                        
                        <div>
                            <Label htmlFor="serviceCenter">Service Centre for Card Collection</Label>
                            <Input
                                id="serviceCenter"
                                value={formData.serviceCenter}
                                onChange={(e) => handleInputChange('serviceCenter', e.target.value)}
                                placeholder="Enter service centre"
                            />
                        </div>
                    </div>
                    
                    <div className="mt-4">
                        <Label>Currency of Account (Please mark (X) the appropriate boxes)</Label>
                        <div className="flex gap-4 mt-2">
                            {['ZWL$', 'USD', 'ZAR', 'BWP', 'EURO', 'OTHER (Indicate)'].map((currency) => (
                                <label key={currency} className="flex items-center space-x-2">
                                    <Checkbox
                                        checked={formData.accountCurrency === currency}
                                        onCheckedChange={(checked) => checked && handleInputChange('accountCurrency', currency)}
                                    />
                                    <span className="text-sm">{currency}</span>
                                </label>
                            ))}
                        </div>
                    </div>
                </Card>

                {/* Personal Details */}
                <Card className="p-6">
                    <div className="flex items-center mb-4">
                        <User className="h-6 w-6 text-emerald-600 mr-3" />
                        <h3 className="text-lg font-semibold">Personal Details</h3>
                    </div>
                    
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        <div>
                            <Label htmlFor="title">Title</Label>
                            <Select value={formData.title} onValueChange={(value) => handleInputChange('title', value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Select title" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="Mr">Mr</SelectItem>
                                    <SelectItem value="Mrs">Mrs</SelectItem>
                                    <SelectItem value="Ms">Ms</SelectItem>
                                    <SelectItem value="Dr">Dr</SelectItem>
                                    <SelectItem value="Prof">Prof</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        
                        <div>
                            <Label htmlFor="firstName">First Name</Label>
                            <Input
                                id="firstName"
                                value={formData.firstName}
                                onChange={(e) => handleInputChange('firstName', e.target.value)}
                                required
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="surname">Surname</Label>
                            <Input
                                id="surname"
                                value={formData.surname}
                                onChange={(e) => handleInputChange('surname', e.target.value)}
                                required
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="maidenName">Maiden Name</Label>
                            <Input
                                id="maidenName"
                                value={formData.maidenName}
                                onChange={(e) => handleInputChange('maidenName', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="gender">Gender</Label>
                            <Select value={formData.gender} onValueChange={(value) => handleInputChange('gender', value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Select gender" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="Male">Male</SelectItem>
                                    <SelectItem value="Female">Female</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        
                        <div>
                            <Label htmlFor="dateOfBirth">Date of Birth</Label>
                            <Input
                                id="dateOfBirth"
                                type="date"
                                value={formData.dateOfBirth}
                                onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                                required
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="placeOfBirth">Place of Birth</Label>
                            <Input
                                id="placeOfBirth"
                                value={formData.placeOfBirth}
                                onChange={(e) => handleInputChange('placeOfBirth', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="nationality">Nationality</Label>
                            <Input
                                id="nationality"
                                value={formData.nationality}
                                onChange={(e) => handleInputChange('nationality', e.target.value)}
                                placeholder="e.g., Zimbabwean"
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="maritalStatus">Marital Status</Label>
                            <Select value={formData.maritalStatus} onValueChange={(value) => handleInputChange('maritalStatus', value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Select status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="Single">Single</SelectItem>
                                    <SelectItem value="Married">Married</SelectItem>
                                    <SelectItem value="Other">Other</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        
                        <div>
                            <Label htmlFor="nationalIdNumber">National ID Number</Label>
                            <Input
                                id="nationalIdNumber"
                                value={formData.nationalIdNumber}
                                onChange={(e) => handleInputChange('nationalIdNumber', e.target.value)}
                                required
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="driversLicense">Driver's License No</Label>
                            <Input
                                id="driversLicense"
                                value={formData.driversLicense}
                                onChange={(e) => handleInputChange('driversLicense', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="passportNumber">Passport Number (if applicable)</Label>
                            <Input
                                id="passportNumber"
                                value={formData.passportNumber}
                                onChange={(e) => handleInputChange('passportNumber', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="passportExpiry">Expiry Date</Label>
                            <Input
                                id="passportExpiry"
                                type="date"
                                value={formData.passportExpiry}
                                onChange={(e) => handleInputChange('passportExpiry', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="countryOfResidence">Country of Residence</Label>
                            <Input
                                id="countryOfResidence"
                                value={formData.countryOfResidence}
                                onChange={(e) => handleInputChange('countryOfResidence', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="highestEducation">Highest Educational Qualification</Label>
                            <Input
                                id="highestEducation"
                                value={formData.highestEducation}
                                onChange={(e) => handleInputChange('highestEducation', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="hobbies">Hobbies</Label>
                            <Input
                                id="hobbies"
                                value={formData.hobbies}
                                onChange={(e) => handleInputChange('hobbies', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="citizenship">Citizenship</Label>
                            <Input
                                id="citizenship"
                                value={formData.citizenship}
                                onChange={(e) => handleInputChange('citizenship', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="dependents">Dependents</Label>
                            <Input
                                id="dependents"
                                type="number"
                                value={formData.dependents}
                                onChange={(e) => handleInputChange('dependents', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="otherNames">Other Names</Label>
                            <Input
                                id="otherNames"
                                value={formData.otherNames}
                                onChange={(e) => handleInputChange('otherNames', e.target.value)}
                            />
                        </div>
                    </div>
                </Card>

                {/* Contact Details */}
                <Card className="p-6">
                    <div className="flex items-center mb-4">
                        <Smartphone className="h-6 w-6 text-emerald-600 mr-3" />
                        <h3 className="text-lg font-semibold">Contact Details</h3>
                    </div>
                    
                    <div className="grid gap-4 md:grid-cols-2">
                        <div className="md:col-span-2">
                            <Label htmlFor="residentialAddress">Residential Address</Label>
                            <Textarea
                                id="residentialAddress"
                                value={formData.residentialAddress}
                                onChange={(e) => handleInputChange('residentialAddress', e.target.value)}
                                rows={3}
                                required
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="telephoneRes">Telephone (Res)</Label>
                            <Input
                                id="telephoneRes"
                                value={formData.telephoneRes}
                                onChange={(e) => handleInputChange('telephoneRes', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="mobile">Mobile (+263-)</Label>
                            <Input
                                id="mobile"
                                value={formData.mobile}
                                onChange={(e) => handleInputChange('mobile', e.target.value)}
                                placeholder="+263-"
                                required
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="emailAddress">Email Address</Label>
                            <Input
                                id="emailAddress"
                                type="email"
                                value={formData.emailAddress}
                                onChange={(e) => handleInputChange('emailAddress', e.target.value)}
                                required
                            />
                        </div>
                    </div>
                </Card>

                {/* Employment Details */}
                <Card className="p-6">
                    <div className="flex items-center mb-4">
                        <Building className="h-6 w-6 text-emerald-600 mr-3" />
                        <h3 className="text-lg font-semibold">Employment Details</h3>
                    </div>
                    
                    <div className="grid gap-4 md:grid-cols-2">
                        <div>
                            <Label htmlFor="employerName">Employer Name</Label>
                            <Input
                                id="employerName"
                                value={formData.employerName}
                                onChange={(e) => handleInputChange('employerName', e.target.value)}
                                required
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="occupation">Occupation</Label>
                            <Input
                                id="occupation"
                                value={formData.occupation}
                                onChange={(e) => handleInputChange('occupation', e.target.value)}
                                required
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="employmentStatus">Employment Status</Label>
                            <Select value={formData.employmentStatus} onValueChange={(value) => handleInputChange('employmentStatus', value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Select status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="Permanent">Permanent</SelectItem>
                                    <SelectItem value="Contract">Contract</SelectItem>
                                    <SelectItem value="Pensioner">Pensioner</SelectItem>
                                    <SelectItem value="Unemployed">Unemployed</SelectItem>
                                    <SelectItem value="Self-Employed">Self-Employed</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        
                        <div>
                            <Label htmlFor="grossMonthlySalary">Gross Monthly Salary</Label>
                            <Input
                                id="grossMonthlySalary"
                                type="number"
                                value={formData.grossMonthlySalary}
                                onChange={(e) => handleInputChange('grossMonthlySalary', e.target.value)}
                            />
                        </div>
                    </div>
                </Card>

                {/* Spouse/Next of Kin */}
                <Card className="p-6">
                    <div className="flex items-center mb-4">
                        <Users className="h-6 w-6 text-emerald-600 mr-3" />
                        <h3 className="text-lg font-semibold">Spouse/Next of Kin</h3>
                    </div>
                    
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        <div>
                            <Label htmlFor="spouseTitle">Title</Label>
                            <Select value={formData.spouseTitle} onValueChange={(value) => handleInputChange('spouseTitle', value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Select title" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="Mr">Mr</SelectItem>
                                    <SelectItem value="Mrs">Mrs</SelectItem>
                                    <SelectItem value="Ms">Ms</SelectItem>
                                    <SelectItem value="Dr">Dr</SelectItem>
                                    <SelectItem value="Prof">Prof</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        
                        <div>
                            <Label htmlFor="spouseFirstName">Full Name</Label>
                            <Input
                                id="spouseFirstName"
                                value={formData.spouseFirstName}
                                onChange={(e) => handleInputChange('spouseFirstName', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="spouseRelationship">Nature of Relationship</Label>
                            <Input
                                id="spouseRelationship"
                                value={formData.spouseRelationship}
                                onChange={(e) => handleInputChange('spouseRelationship', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="spouseIdNumber">National ID No</Label>
                            <Input
                                id="spouseIdNumber"
                                value={formData.spouseIdNumber}
                                onChange={(e) => handleInputChange('spouseIdNumber', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="spouseContact">Contact Number</Label>
                            <Input
                                id="spouseContact"
                                value={formData.spouseContact}
                                onChange={(e) => handleInputChange('spouseContact', e.target.value)}
                            />
                        </div>
                    </div>
                </Card>

                {/* ZB Life Funeral Cash Cover */}
                <Card className="p-6">
                    <div className="bg-emerald-100 p-4 rounded-lg mb-4">
                        <h3 className="text-lg font-semibold text-emerald-800">H - ZB LIFE FUNERAL CASH COVER</h3>
                        <p className="text-sm text-emerald-700">
                            Details of dependents to be covered by this application is up to eight (8) dependents. 
                            <em>Please tick (√) the appropriate box to show supplementary benefits to be included.</em>
                        </p>
                    </div>
                    
                    <div className="overflow-x-auto">
                        <table className="w-full border-collapse border border-gray-300">
                            <thead>
                                <tr className="bg-gray-50">
                                    <th className="border border-gray-300 p-2 text-left">Surname</th>
                                    <th className="border border-gray-300 p-2 text-left">Forename(s)</th>
                                    <th className="border border-gray-300 p-2 text-left">Relationship</th>
                                    <th className="border border-gray-300 p-2 text-left">Date of Birth</th>
                                    <th className="border border-gray-300 p-2 text-left">Birth Entry/National ID No.</th>
                                    <th className="border border-gray-300 p-2 text-left">Cover Amount Per Dependant $</th>
                                    <th className="border border-gray-300 p-2 text-left">Premium Per Month $</th>
                                </tr>
                            </thead>
                            <tbody>
                                {[...Array(8)].map((_, index) => (
                                    <tr key={index}>
                                        <td className="border border-gray-300 p-2">
                                            <Input className="w-full" placeholder="Surname" />
                                        </td>
                                        <td className="border border-gray-300 p-2">
                                            <Input className="w-full" placeholder="Forename(s)" />
                                        </td>
                                        <td className="border border-gray-300 p-2">
                                            <Input className="w-full" placeholder="Relationship" />
                                        </td>
                                        <td className="border border-gray-300 p-2">
                                            <Input type="date" className="w-full" />
                                        </td>
                                        <td className="border border-gray-300 p-2">
                                            <Input className="w-full" placeholder="ID Number" />
                                        </td>
                                        <td className="border border-gray-300 p-2">
                                            <Input type="number" className="w-full" placeholder="Amount" />
                                        </td>
                                        <td className="border border-gray-300 p-2">
                                            <Input type="number" className="w-full" placeholder="Premium" />
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                    
                    <div className="mt-4 grid gap-4 md:grid-cols-2">
                        <div>
                            <h4 className="font-semibold mb-2">Principal Member</h4>
                            <div className="space-y-2 text-sm">
                                <div className="flex justify-between">
                                    <span>Memorial Cash Benefit:</span>
                                    <Input className="w-20" placeholder="Amount" />
                                </div>
                                <div className="flex justify-between">
                                    <span>Tombstone Cash Benefit:</span>
                                    <Input className="w-20" placeholder="Amount" />
                                </div>
                                <div className="flex justify-between">
                                    <span>Grocery Benefit:</span>
                                    <Input className="w-20" placeholder="Amount" />
                                </div>
                                <div className="flex justify-between">
                                    <span>School Fees Benefit:</span>
                                    <Input className="w-20" placeholder="Amount" />
                                </div>
                                <div className="flex justify-between">
                                    <span>Personal Accident Benefit:</span>
                                    <Input className="w-20" placeholder="Amount" />
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <h4 className="font-semibold mb-2">Supplementary Benefits (Tick (√) appropriate box)</h4>
                            <div className="space-y-2 text-sm">
                                <div className="flex items-center space-x-2">
                                    <Checkbox id="memorialCash" />
                                    <Label htmlFor="memorialCash">Memorial Cash Benefit: Amount of Cover Per Person</Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <Checkbox id="tombstoneCash" />
                                    <Label htmlFor="tombstoneCash">Tombstone Cash Benefit: Amount of Cover Per Person</Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <Checkbox id="groceryBenefit" />
                                    <Label htmlFor="groceryBenefit">Grocery Benefit: Amount of Cover</Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <Checkbox id="schoolFees" />
                                    <Label htmlFor="schoolFees">School Fees Benefit: Amount of Cover</Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <Checkbox id="personalAccident" />
                                    <Label htmlFor="personalAccident">Personal Accident Benefit: Please supply details below</Label>
                                </div>
                            </div>
                        </div>
                    </div>
                </Card>

                {/* Personal Accident Benefit */}
                <Card className="p-6">
                    <div className="bg-emerald-100 p-4 rounded-lg mb-4">
                        <h3 className="text-lg font-semibold text-emerald-800">I - PERSONAL ACCIDENT BENEFIT</h3>
                    </div>
                    
                    <div className="grid gap-4 md:grid-cols-2">
                        <div>
                            <Label htmlFor="accidentSurname">Surname</Label>
                            <Input
                                id="accidentSurname"
                                value={formData.personalAccidentBenefit.surname}
                                onChange={(e) => handleInputChange('personalAccidentBenefit.surname', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="accidentForenames">Forename(s)</Label>
                            <Input
                                id="accidentForenames"
                                value={formData.personalAccidentBenefit.forenames}
                                onChange={(e) => handleInputChange('personalAccidentBenefit.forenames', e.target.value)}
                            />
                        </div>
                    </div>
                </Card>

                {/* Digital Banking Services */}
                <Card className="p-6">
                    <div className="flex items-center mb-4">
                        <Smartphone className="h-6 w-6 text-emerald-600 mr-3" />
                        <h3 className="text-lg font-semibold">Digital Banking Services</h3>
                    </div>
                    
                    <div className="space-y-4">
                        <div className="flex items-center space-x-3">
                            <Checkbox
                                id="mobileMoneyEcocash"
                                checked={formData.mobileMoneyEcocash}
                                onCheckedChange={(checked) => handleInputChange('mobileMoneyEcocash', checked)}
                            />
                            <Label htmlFor="mobileMoneyEcocash">Mobile money e.g. Ecocash Services</Label>
                        </div>
                        
                        {formData.mobileMoneyEcocash && (
                            <div className="ml-6">
                                <Label htmlFor="mobileMoneyNumber">Mobile Number</Label>
                                <Input
                                    id="mobileMoneyNumber"
                                    value={formData.mobileMoneyNumber}
                                    onChange={(e) => handleInputChange('mobileMoneyNumber', e.target.value)}
                                    placeholder="263..."
                                />
                            </div>
                        )}
                        
                        <div className="flex items-center space-x-3">
                            <Checkbox
                                id="whatsappBanking"
                                checked={formData.whatsappBanking}
                                onCheckedChange={(checked) => handleInputChange('whatsappBanking', checked)}
                            />
                            <Label htmlFor="whatsappBanking">WhatsApp Banking</Label>
                        </div>
                        
                        <div className="flex items-center space-x-3">
                            <Checkbox
                                id="internetBanking"
                                checked={formData.internetBanking}
                                onCheckedChange={(checked) => handleInputChange('internetBanking', checked)}
                            />
                            <Label htmlFor="internetBanking">Internet Banking</Label>
                        </div>
                    </div>
                </Card>

                {/* Supporting KYC Checklist */}
                <Card className="p-6">
                    <div className="bg-emerald-100 p-4 rounded-lg mb-4">
                        <h3 className="text-lg font-semibold text-emerald-800">SUPPORTING KYC CHECKLIST</h3>
                        <p className="text-sm text-emerald-700">Please attach certified copies of the following and indicate by marking:</p>
                    </div>
                    
                    <div className="grid gap-4 md:grid-cols-2">
                        <div className="space-y-3">
                            <div className="flex items-center space-x-2">
                                <Checkbox
                                    id="passportPhotos"
                                    checked={formData.supportingDocs.passportPhotos}
                                    onCheckedChange={(checked) => handleInputChange('supportingDocs.passportPhotos', checked)}
                                />
                                <Label htmlFor="passportPhotos">(i) Two (2) recent passport-sized photos</Label>
                            </div>
                            
                            <div className="flex items-center space-x-2">
                                <Checkbox
                                    id="proofOfResidence"
                                    checked={formData.supportingDocs.proofOfResidence}
                                    onCheckedChange={(checked) => handleInputChange('supportingDocs.proofOfResidence', checked)}
                                />
                                <Label htmlFor="proofOfResidence">(ii) Proof of residence (within 3-months)</Label>
                            </div>
                            
                            <div className="flex items-center space-x-2">
                                <Checkbox
                                    id="payslip"
                                    checked={formData.supportingDocs.payslip}
                                    onCheckedChange={(checked) => handleInputChange('supportingDocs.payslip', checked)}
                                />
                                <Label htmlFor="payslip">(iii) Payslip (where applicable)</Label>
                            </div>
                        </div>
                        
                        <div className="space-y-3">
                            <p className="font-medium">Current Identification Documents: (mark applicable):</p>
                            <div className="flex items-center space-x-2">
                                <Checkbox
                                    id="nationalIdCard"
                                    checked={formData.supportingDocs.nationalId}
                                    onCheckedChange={(checked) => handleInputChange('supportingDocs.nationalId', checked)}
                                />
                                <Label htmlFor="nationalIdCard">National ID Card</Label>
                            </div>
                            
                            <div className="flex items-center space-x-2">
                                <Checkbox
                                    id="passportDoc"
                                    checked={formData.supportingDocs.passport}
                                    onCheckedChange={(checked) => handleInputChange('supportingDocs.passport', checked)}
                                />
                                <Label htmlFor="passportDoc">Passport</Label>
                            </div>
                            
                            <div className="flex items-center space-x-2">
                                <Checkbox
                                    id="driversLicenseDoc"
                                    checked={formData.supportingDocs.driversLicense}
                                    onCheckedChange={(checked) => handleInputChange('supportingDocs.driversLicense', checked)}
                                />
                                <Label htmlFor="driversLicenseDoc">Drivers' License</Label>
                            </div>
                        </div>
                    </div>
                </Card>

                {/* Declaration */}
                <Card className="p-6">
                    <div className="bg-emerald-100 p-4 rounded-lg mb-4">
                        <h3 className="text-lg font-semibold text-emerald-800">K - DECLARATION</h3>
                        <p className="text-sm text-emerald-700">
                            I confirm that to the best of my knowledge, the above information is true and correct and that all the persons registered above are not on medication for any disease or illness. Should anything change, I undertake to advise ZB Bank immediately.
                        </p>
                    </div>
                    
                    <div className="grid gap-4 md:grid-cols-3">
                        <div>
                            <Label htmlFor="declarationName">Full Name</Label>
                            <Input
                                id="declarationName"
                                value={formData.declaration.fullName}
                                onChange={(e) => handleInputChange('declaration.fullName', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="declarationSignature">Applicant's Signature</Label>
                            <div className="h-20 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center text-gray-500">
                                Signature Area
                            </div>
                        </div>
                        
                        <div>
                            <Label htmlFor="declarationDate">Date</Label>
                            <Input
                                id="declarationDate"
                                type="date"
                                value={formData.declaration.date}
                                onChange={(e) => handleInputChange('declaration.date', e.target.value)}
                            />
                        </div>
                    </div>
                </Card>

                <div className="flex justify-between pt-4">
                    <Button
                        type="button"
                        variant="outline"
                        onClick={onBack}
                        disabled={loading}
                        className="flex items-center gap-2"
                    >
                        <ChevronLeft className="h-4 w-4" />
                        Back
                    </Button>
                    
                    <Button
                        type="submit"
                        disabled={loading}
                        className="bg-emerald-600 hover:bg-emerald-700 px-8"
                    >
                        {loading ? 'Submitting...' : 'Submit Application'}
                    </Button>
                </div>
            </form>
        </div>
    );
};

export default ZBAccountOpeningForm;