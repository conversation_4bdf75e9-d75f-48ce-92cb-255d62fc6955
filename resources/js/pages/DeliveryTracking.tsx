import { Head, router } from '@inertiajs/react';
import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { 
    Search, 
    Package, 
    Truck, 
    MapPin, 
    CheckCircle, 
    Clock, 
    ArrowLeft,
    Phone,
    Calendar,
    User,
    Navigation
} from 'lucide-react';
import { Link } from '@inertiajs/react';

interface DeliveryDetails {
    sessionId: string;
    status: 'processing' | 'dispatched' | 'in_transit' | 'out_for_delivery' | 'delivered' | 'failed';
    product: string;
    recipientName: string;
    deliveryAddress: string;
    trackingNumber: string;
    estimatedDelivery: string;
    actualDelivery?: string;
    driverName?: string;
    driverPhone?: string;
    currentLocation?: string;
    timeline: DeliveryEvent[];
    deliveryNotes?: string;
}

interface DeliveryEvent {
    id: number;
    title: string;
    location: string;
    timestamp: string;
    status: 'completed' | 'current' | 'pending';
}

export default function DeliveryTracking() {
    const [searchQuery, setSearchQuery] = useState('');
    const [searching, setSearching] = useState(false);
    const [deliveryDetails, setDeliveryDetails] = useState<DeliveryDetails | null>(null);
    const [error, setError] = useState('');

    // Check if reference number is passed in the URL
    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const ref = urlParams.get('ref');
        if (ref) {
            setSearchQuery(ref);
            handleSearch(ref);
        }
    }, []);

    const handleSearch = async (reference?: string) => {
        const searchRef = reference || searchQuery;
        
        if (!searchRef.trim()) {
            setError('Please enter a reference or tracking number');
            return;
        }

        setSearching(true);
        setError('');
        
        try {
            const response = await fetch(`/api/delivery/tracking/${searchRef}`);
            if (response.ok) {
                const data = await response.json();
                setDeliveryDetails(data);
            } else if (response.status === 404) {
                setError('Delivery not found. Please check your reference number.');
                setDeliveryDetails(null);
            } else {
                setError('An error occurred. Please try again.');
                setDeliveryDetails(null);
            }
        } catch (err) {
            setError('Failed to fetch delivery status. Please try again.');
            setDeliveryDetails(null);
        } finally {
            setSearching(false);
        }
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'processing':
                return (
                    <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                        <Package className="h-5 w-5" />
                        <span className="font-medium">Order Processing</span>
                    </div>
                );
            case 'dispatched':
                return (
                    <div className="flex items-center gap-2 text-blue-600 dark:text-blue-400">
                        <Truck className="h-5 w-5" />
                        <span className="font-medium">Dispatched</span>
                    </div>
                );
            case 'in_transit':
                return (
                    <div className="flex items-center gap-2 text-yellow-600 dark:text-yellow-400">
                        <Truck className="h-5 w-5" />
                        <span className="font-medium">In Transit</span>
                    </div>
                );
            case 'out_for_delivery':
                return (
                    <div className="flex items-center gap-2 text-orange-600 dark:text-orange-400">
                        <Navigation className="h-5 w-5" />
                        <span className="font-medium">Out for Delivery</span>
                    </div>
                );
            case 'delivered':
                return (
                    <div className="flex items-center gap-2 text-green-600 dark:text-green-400">
                        <CheckCircle className="h-5 w-5" />
                        <span className="font-medium">Delivered</span>
                    </div>
                );
            case 'failed':
                return (
                    <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
                        <Clock className="h-5 w-5" />
                        <span className="font-medium">Delivery Failed</span>
                    </div>
                );
            default:
                return null;
        }
    };

    const getTimelineIcon = (status: string) => {
        switch (status) {
            case 'completed':
                return <CheckCircle className="h-5 w-5 text-emerald-600" />;
            case 'current':
                return <Navigation className="h-5 w-5 text-blue-600 animate-pulse" />;
            case 'pending':
                return <div className="h-5 w-5 rounded-full border-2 border-gray-300" />;
            default:
                return null;
        }
    };

    const getDeliveryProgress = () => {
        if (!deliveryDetails) return 0;
        
        const statusOrder = ['processing', 'dispatched', 'in_transit', 'out_for_delivery', 'delivered'];
        const currentIndex = statusOrder.indexOf(deliveryDetails.status);
        return ((currentIndex + 1) / statusOrder.length) * 100;
    };

    return (
        <>
            <Head title="Delivery Tracking" />
            <div className="min-h-screen bg-[#FDFDFC] dark:bg-[#0a0a0a]">
                <div className="max-w-4xl mx-auto px-4 py-8">
                    {/* Header */}
                    <div className="mb-8">
                        <Link 
                            href="/"
                            className="inline-flex items-center gap-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 mb-4"
                        >
                            <ArrowLeft className="h-4 w-4" />
                            Back to Home
                        </Link>
                        <h1 className="text-3xl font-semibold text-gray-900 dark:text-gray-100">
                            Track Your Delivery
                        </h1>
                        <p className="text-gray-600 dark:text-gray-400 mt-2">
                            Enter your reference or tracking number to track your delivery
                        </p>
                    </div>

                    {/* Search Section */}
                    <Card className="p-6 mb-8">
                        <div className="max-w-xl mx-auto">
                            <Label htmlFor="tracking" className="text-lg mb-2 block">
                                Reference or Tracking Number
                            </Label>
                            <div className="flex gap-3">
                                <Input
                                    id="tracking"
                                    placeholder="e.g., ZB2025000123 or TRK123456"
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                                    className="text-lg"
                                />
                                <Button 
                                    onClick={() => handleSearch()}
                                    disabled={searching}
                                    size="lg"
                                    className="bg-emerald-600 hover:bg-emerald-700"
                                >
                                    <Search className="h-5 w-5 mr-2" />
                                    {searching ? 'Searching...' : 'Track'}
                                </Button>
                            </div>
                            {error && (
                                <p className="text-red-600 dark:text-red-400 mt-2 text-sm">
                                    {error}
                                </p>
                            )}
                        </div>
                    </Card>

                    {/* Results Section */}
                    {deliveryDetails && (
                        <div className="space-y-6">
                            {/* Delivery Overview */}
                            <Card className="p-6">
                                <div className="flex items-center justify-between mb-6">
                                    <h2 className="text-xl font-semibold">Delivery Overview</h2>
                                    {getStatusBadge(deliveryDetails.status)}
                                </div>
                                
                                {/* Progress Bar */}
                                <div className="mb-6">
                                    <div className="flex h-2 overflow-hidden rounded-full bg-gray-200 dark:bg-gray-800">
                                        <div 
                                            className="transition-all duration-500 bg-emerald-600"
                                            style={{ width: `${getDeliveryProgress()}%` }}
                                        />
                                    </div>
                                    <div className="flex justify-between mt-2 text-xs text-gray-500">
                                        <span>Processing</span>
                                        <span>Dispatched</span>
                                        <span>In Transit</span>
                                        <span>Out for Delivery</span>
                                        <span>Delivered</span>
                                    </div>
                                </div>
                                
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <p className="text-sm text-gray-500 dark:text-gray-400">Tracking Number</p>
                                        <p className="font-medium">{deliveryDetails.trackingNumber}</p>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-500 dark:text-gray-400">Product</p>
                                        <p className="font-medium">{deliveryDetails.product}</p>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-500 dark:text-gray-400">Recipient</p>
                                        <p className="font-medium">{deliveryDetails.recipientName}</p>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-500 dark:text-gray-400">Delivery Address</p>
                                        <p className="font-medium">{deliveryDetails.deliveryAddress}</p>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-500 dark:text-gray-400">
                                            {deliveryDetails.status === 'delivered' ? 'Delivered On' : 'Estimated Delivery'}
                                        </p>
                                        <p className="font-medium">
                                            {deliveryDetails.status === 'delivered' 
                                                ? deliveryDetails.actualDelivery 
                                                : deliveryDetails.estimatedDelivery}
                                        </p>
                                    </div>
                                    {deliveryDetails.currentLocation && (
                                        <div>
                                            <p className="text-sm text-gray-500 dark:text-gray-400">Current Location</p>
                                            <p className="font-medium">{deliveryDetails.currentLocation}</p>
                                        </div>
                                    )}
                                </div>

                                {/* Delivery Driver Info */}
                                {deliveryDetails.status === 'out_for_delivery' && deliveryDetails.driverName && (
                                    <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                                        <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-3">
                                            Delivery Driver Information
                                        </h3>
                                        <div className="grid grid-cols-2 gap-3">
                                            <div className="flex items-center gap-2">
                                                <User className="h-4 w-4 text-blue-600" />
                                                <div>
                                                    <p className="text-xs text-blue-700 dark:text-blue-300">Driver Name</p>
                                                    <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                                                        {deliveryDetails.driverName}
                                                    </p>
                                                </div>
                                            </div>
                                            {deliveryDetails.driverPhone && (
                                                <div className="flex items-center gap-2">
                                                    <Phone className="h-4 w-4 text-blue-600" />
                                                    <div>
                                                        <p className="text-xs text-blue-700 dark:text-blue-300">Contact</p>
                                                        <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                                                            {deliveryDetails.driverPhone}
                                                        </p>
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                )}

                                {/* Delivery Notes */}
                                {deliveryDetails.deliveryNotes && (
                                    <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-900/20 rounded-lg">
                                        <p className="text-sm font-medium text-gray-800 dark:text-gray-200 mb-1">
                                            Delivery Notes:
                                        </p>
                                        <p className="text-sm text-gray-700 dark:text-gray-300">
                                            {deliveryDetails.deliveryNotes}
                                        </p>
                                    </div>
                                )}
                            </Card>

                            {/* Delivery Timeline */}
                            <Card className="p-6">
                                <h2 className="text-xl font-semibold mb-6">Delivery Timeline</h2>
                                <div className="space-y-4">
                                    {deliveryDetails.timeline.map((event, index) => (
                                        <div key={event.id} className="flex gap-4">
                                            <div className="flex flex-col items-center">
                                                {getTimelineIcon(event.status)}
                                                {index < deliveryDetails.timeline.length - 1 && (
                                                    <div className={`flex-1 w-0.5 mt-2 ${
                                                        event.status === 'completed' 
                                                            ? 'bg-emerald-600' 
                                                            : 'bg-gray-300 dark:bg-gray-700'
                                                    }`} />
                                                )}
                                            </div>
                                            <div className="flex-1 pb-8">
                                                <h3 className="font-medium text-gray-900 dark:text-gray-100">
                                                    {event.title}
                                                </h3>
                                                <div className="flex items-center gap-2 mt-1">
                                                    <MapPin className="h-3 w-3 text-gray-400" />
                                                    <p className="text-sm text-gray-600 dark:text-gray-400">
                                                        {event.location}
                                                    </p>
                                                </div>
                                                <div className="flex items-center gap-2 mt-1">
                                                    <Calendar className="h-3 w-3 text-gray-400" />
                                                    <p className="text-xs text-gray-500 dark:text-gray-500">
                                                        {event.timestamp}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </Card>

                            {/* Actions */}
                            <Card className="p-6">
                                <h2 className="text-xl font-semibold mb-4">Need Help?</h2>
                                <div className="space-y-3">
                                    <p className="text-sm text-gray-600 dark:text-gray-400">
                                        If you have any questions about your delivery, please contact our support team.
                                    </p>
                                    <div className="flex gap-3">
                                        <Button 
                                            variant="outline"
                                            className="flex-1"
                                        >
                                            <Phone className="h-4 w-4 mr-2" />
                                            Call Support
                                        </Button>
                                        <Button 
                                            variant="outline"
                                            className="flex-1"
                                        >
                                            Contact Delivery Team
                                        </Button>
                                    </div>
                                </div>
                            </Card>
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}