<?php

namespace App\Http\Controllers;

use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class PdfController extends Controller
{
    /**
     * Generate and download the SSB form PDF
     */
    public function downloadSsbForm(Request $request)
    {
        // Sample data for the form - in a real application, this would come from the database or request
        $data = [
            // Delivery status and agent info
            'delivery_status' => 'Future',
            'agent' => '<PERSON>',
            'province' => 'Harare',
            'team' => 'Team A',
            
            // Customer personal details
            'surname' => 'Maposhere',
            'firstname' => '<PERSON><PERSON><PERSON><PERSON>',
            'dob' => '01/01/1990',
            'idnumber' => '63-123456A63',
            'cellnumber' => '+263 77 123 4567',
            'whatsapp' => '+263 77 123 4567',
            'email' => '<EMAIL>',
            'ministry' => 'Education',
            'employer' => 'Ministry of Education',
            'employer_address' => '1 Causeway, Harare',
            'permanent_address' => '123 Main Street, Harare',
            'jobtitle' => 'Teacher',
            'employment_date' => '01/01/2020',
            'employment_number' => 'EMP001',
            'salary' => '500.00',
            
            // Next of kin details
            'kin1_name' => 'Jane Doe',
            'kin1_relationship' => 'Spouse',
            'kin1_phone' => '+263 77 987 6543',
            'kin1_address' => '123 Main Street, Harare',
            'kin2_name' => 'John Smith',
            'kin2_relationship' => 'Brother',
            'kin2_phone' => '+263 77 555 1234',
            'kin2_address' => '456 Second Street, Harare',
            'kin3_name' => 'Mary Johnson',
            'kin3_relationship' => 'Sister',
            'kin3_phone' => '+263 77 444 5678',
            'kin3_address' => '789 Third Street, Harare',
            
            // Banking details
            'bank' => 'ZB Bank',
            'branch' => 'Harare',
            'account_number' => '**********',
            
            // Loan details
            'loan1_instalment' => '50.00',
            'loan1_balance' => '500.00',
            'loan1_maturity' => '31/12/2024',
            'loan2_instalment' => '',
            'loan2_balance' => '',
            'loan2_maturity' => '',
            
            // Credit facility application
            'applied_amount' => '1000.00',
            'purpose_line1' => 'Purchase of household items',
            'purpose_line2' => 'Kitchen appliances and furniture',
            'purpose_line3' => 'Home improvement',
            
            // Declaration details
            'declaration_name' => 'Tapiwanashe Maposhere',
            'declaration_signature' => 'T. Maposhere',
            'declaration_date' => date('d/m/Y'),
            'witness_name' => 'Jane Doe',
            'witness_signature' => 'J. Doe',
            'witness_date' => date('d/m/Y'),
            
            // Official use
            'received_name' => '',
            'received_signature' => '',
            'received_date' => '',
            'approved_name' => '',
            'approved_signature' => '',
            'approved_date' => '',
            
            // TY30 form data
            'ty30_firstname' => 'Tapiwanashe',
            'ty30_surname' => 'Maposhere',
            'ty30_idnumber' => '63-123456A63',
            'ty30_ministry' => 'Ministry of Education',
            'ty30_province' => 'Harare',
            'ty30_employee_code' => '123456',
            'ty30_check_letter' => 'A',
            'ty30_dept_code' => '1234',
            'ty30_station_code' => '5678',
            'ty30_payee_code' => '90123',
            'ty30_monthly_rate' => '100.00',
            'ty30_from_date' => '01012024',
            'ty30_to_date' => '31122024',
            'ty30_contract_date' => date('d/m/Y'),
            'ty30_declaration_name' => 'Tapiwanashe Maposhere',
            'ty30_declaration_signature' => 'T. Maposhere',
            'ty30_declaration_date' => date('d/m/Y'),
            'ty30_auth1_name' => '',
            'ty30_auth1_signature' => '',
            'ty30_auth1_date' => '',
            'ty30_auth2_name' => '',
            'ty30_auth2_signature' => '',
            'ty30_auth2_date' => '',
            
            // POF form data
            'pof_date' => date('d/m/Y'),
            'pof_client_name' => 'Tapiwanashe Maposhere',
            'pof_ec_number' => '123456',
            'pof_delivery_address' => '123 Main Street, Harare',
            'pof_item1_desc' => 'Refrigerator',
            'pof_item1_code' => 'REF001',
            'pof_item1_qty' => '1',
            'pof_item1_instalment' => '50.00',
            'pof_item2_desc' => 'Washing Machine',
            'pof_item2_code' => 'WM001',
            'pof_item2_qty' => '1',
            'pof_item2_instalment' => '40.00',
            'pof_item3_desc' => '',
            'pof_item3_code' => '',
            'pof_item3_qty' => '',
            'pof_item3_instalment' => '',
            'pof_total_qty' => '2',
            'pof_total_instalment' => '90.00',
            'pof_signature' => 'T. Maposhere',
            'pof_id_number' => '63-123456A63',
        ];
        
        // Load the HTML template and pass the data
        $html = view('forms.ssb_form_pdf', $data)->render();
        
        // Generate PDF
        $pdf = Pdf::loadHTML($html);
        $pdf->setPaper('A4', 'portrait');
        
        // Return the PDF as a download
        return $pdf->download('ssb_loan_application_form.pdf');
    }

    /**
     * Generate and download the ZB Account Opening form PDF
     */
    public function downloadZbAccountForm(Request $request)
    {
        // Generate PDF from the Blade view
        $pdf = Pdf::loadView('forms.zb_account_opening_pdf');
        $pdf->setPaper('A4', 'portrait');

        // Return the PDF as a download
        return $pdf->download('ZB_Account_Opening_Form.pdf');
    }

    /**
     * Generate and download the Account Holders form PDF
     */
    public function downloadAccountHoldersForm(Request $request)
    {
        // Generate PDF from the Blade view
        $pdf = Pdf::loadView('forms.account_holders_pdf');
        $pdf->setPaper('A4', 'portrait');

        // Return the PDF as a download
        return $pdf->download('Account_Holders_Application_Form.pdf');
    }

    /**
     * Generate and download the SME Account Opening form PDF
     */
    public function downloadSmeAccountOpeningForm(Request $request)
    {
        // Generate PDF from the Blade view
        $pdf = Pdf::loadView('forms.sme_account_opening_pdf');
        $pdf->setPaper('A4', 'portrait');

        // Return the PDF as a download
        return $pdf->download('SME_Account_Opening_Form.pdf');
    }
}
