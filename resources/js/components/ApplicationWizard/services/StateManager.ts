import axios from 'axios';

export class StateManager {
    private apiUrl = '/api/states';
    
    generateSessionId(): string {
        return `web_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    async saveState(sessionId: string, currentStep: string, formData: any): Promise<void> {
        try {
            await axios.post(`${this.apiUrl}/save`, {
                session_id: sessionId,
                channel: 'web',
                user_identifier: this.getUserIdentifier(),
                current_step: currentStep,
                form_data: formData
            });
        } catch (error) {
            console.error('Error saving state:', error);
            throw error;
        }
    }
    
    async retrieveState(userIdentifier?: string): Promise<any> {
        try {
            const response = await axios.post(`${this.apiUrl}/retrieve`, {
                user: userIdentifier || this.getUserIdentifier(),
                channel: 'web'
            });
            return response.data;
        } catch (error) {
            console.error('Error retrieving state:', error);
            return null;
        }
    }
    
    private getUserIdentifier(): string {
        let identifier = localStorage.getItem('user_identifier');
        if (!identifier) {
            identifier = `web_user_${Date.now()}`;
            localStorage.setItem('user_identifier', identifier);
        }
        return identifier;
    }
}