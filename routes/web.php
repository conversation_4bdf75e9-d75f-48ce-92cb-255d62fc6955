<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Controllers\PdfController;
use App\Http\Controllers\ApplicationWizardController;
use App\Http\Controllers\ApplicationPDFController;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::get('/download-ssb-form', [PdfController::class, 'downloadSsbForm'])->name('download.ssb.form');
Route::get('/download-zb-account-form', [PdfController::class, 'downloadZbAccountForm'])->name('download.zb.account.form');
Route::get('/download-account-holders-form', [PdfController::class, 'downloadAccountHoldersForm'])->name('download.account.holders.form');
Route::get('/download-sme-account-opening-form', [PdfController::class, 'downloadSmeAccountOpeningForm'])->name('download.sme.account.opening.form');

// Application Wizard Routes
Route::get('/application', [ApplicationWizardController::class, 'show'])->name('application.wizard');
Route::get('/application/resume/{identifier}', [ApplicationWizardController::class, 'resume'])->name('application.resume');
Route::get('/application/status', [ApplicationWizardController::class, 'status'])->name('application.status');
Route::get('/delivery/tracking', [ApplicationWizardController::class, 'tracking'])->name('delivery.tracking');
Route::get('/reference-code', [ApplicationWizardController::class, 'referenceCodeLookup'])->name('reference.code.lookup');

// Cross-platform synchronization routes
Route::post('/application/switch-to-whatsapp', [ApplicationWizardController::class, 'switchToWhatsApp'])->name('application.switch.whatsapp');
Route::post('/application/switch-to-web', [ApplicationWizardController::class, 'switchToWeb'])->name('application.switch.web');
Route::get('/application/sync-status', [ApplicationWizardController::class, 'getSyncStatus'])->name('application.sync.status');
Route::post('/application/synchronize', [ApplicationWizardController::class, 'synchronizeData'])->name('application.synchronize');

// PDF Routes
Route::get('/application/download/{sessionId}', [ApplicationPDFController::class, 'download'])->name('application.pdf.download');
Route::get('/application/view/{sessionId}', [ApplicationPDFController::class, 'view'])->name('application.pdf.view');
Route::post('/application/pdf/batch', [ApplicationPDFController::class, 'batchDownload'])->name('application.pdf.batch');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');
    
    // Admin routes
    Route::prefix('admin')->name('admin.')->middleware(['auth', 'verified'])->group(function () {
        Route::get('/', [\App\Http\Controllers\AdminController::class, 'dashboard'])->name('dashboard');

        // Application Management
        Route::prefix('applications')->name('applications.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Admin\ApplicationManagementController::class, 'index'])->name('index');
            Route::get('/{sessionId}', [\App\Http\Controllers\Admin\ApplicationManagementController::class, 'show'])->name('show');
            Route::put('/{sessionId}/status', [\App\Http\Controllers\Admin\ApplicationManagementController::class, 'updateStatus'])->name('update-status');
            Route::post('/{sessionId}/notes', [\App\Http\Controllers\Admin\ApplicationManagementController::class, 'addNote'])->name('add-note');
            Route::get('/{sessionId}/pdf', [\App\Http\Controllers\Admin\ApplicationManagementController::class, 'downloadPdf'])->name('download-pdf');
            Route::post('/bulk-action', [\App\Http\Controllers\Admin\ApplicationManagementController::class, 'bulkAction'])->name('bulk-action');
        });

        // Analytics & Reports
        Route::prefix('analytics')->name('analytics.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Admin\AnalyticsController::class, 'index'])->name('index');
            Route::get('/export', [\App\Http\Controllers\Admin\AnalyticsController::class, 'export'])->name('export');
            Route::get('/channel-performance', [\App\Http\Controllers\Admin\AnalyticsController::class, 'channelPerformance'])->name('channel-performance');
            Route::get('/conversion-funnel', [\App\Http\Controllers\Admin\AnalyticsController::class, 'conversionFunnel'])->name('conversion-funnel');
        });

        // System Management
        Route::prefix('system')->name('system.')->group(function () {
            Route::get('/health', [\App\Http\Controllers\Admin\SystemController::class, 'health'])->name('health');
            Route::get('/logs', [\App\Http\Controllers\Admin\SystemController::class, 'logs'])->name('logs');
            Route::post('/cache/clear', [\App\Http\Controllers\Admin\SystemController::class, 'clearCache'])->name('clear-cache');
            Route::get('/queue/status', [\App\Http\Controllers\Admin\SystemController::class, 'queueStatus'])->name('queue-status');
            Route::post('/maintenance', [\App\Http\Controllers\Admin\SystemController::class, 'toggleMaintenance'])->name('maintenance');
        });

        // User Management
        Route::prefix('users')->name('users.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Admin\UserController::class, 'index'])->name('index');
            Route::post('/', [\App\Http\Controllers\Admin\UserController::class, 'store'])->name('store');
            Route::get('/{user}', [\App\Http\Controllers\Admin\UserController::class, 'show'])->name('show');
            Route::put('/{user}', [\App\Http\Controllers\Admin\UserController::class, 'update'])->name('update');
            Route::delete('/{user}', [\App\Http\Controllers\Admin\UserController::class, 'destroy'])->name('destroy');
        });

        // Settings
        Route::prefix('settings')->name('settings.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Admin\SettingsController::class, 'index'])->name('index');
            Route::put('/general', [\App\Http\Controllers\Admin\SettingsController::class, 'updateGeneral'])->name('update-general');
            Route::put('/notifications', [\App\Http\Controllers\Admin\SettingsController::class, 'updateNotifications'])->name('update-notifications');
            Route::put('/security', [\App\Http\Controllers\Admin\SettingsController::class, 'updateSecurity'])->name('update-security');
        });
    });
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
require __DIR__.'/test.php';
