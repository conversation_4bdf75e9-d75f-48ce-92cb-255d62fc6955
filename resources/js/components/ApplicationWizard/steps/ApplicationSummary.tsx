import React from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { ChevronLeft, CheckCircle, User, Building, DollarSign, Calendar, CreditCard } from 'lucide-react';

interface ApplicationSummaryProps {
    data: any;
    onNext: (data: any) => void;
    onBack: () => void;
    loading?: boolean;
}

const getFormIdByEmployer = (employerId: string, hasAccount: boolean, wantsAccount: boolean) => {
    switch (employerId) {
        case 'goz-ssb':
            return 'ssb_account_opening_form.json';
        case 'goz-pension':
            return 'pensioners_loan_account.json';
        case 'entrepreneur':
            return 'smes_business_account_opening.json';
        default:
            if (hasAccount) {
                return 'account_holder_loan_application.json';
            } else if (wantsAccount) {
                return 'individual_account_opening.json';
            }
            return 'individual_account_opening.json';
    }
};

const ApplicationSummary: React.FC<ApplicationSummaryProps> = ({ data, onNext, onBack, loading }) => {
    const formId = getFormIdByEmployer(data.employer, data.hasAccount, data.wantsAccount);
    
    const handleSubmit = () => {
        onNext({ 
            formId,
            proceedToForm: true
        });
    };
    
    const getEmployerName = (employerId: string) => {
        const employerMap: { [key: string]: string } = {
            'goz-ssb': 'GOZ (Government of Zimbabwe) - SSB',
            'goz-zappa': 'GOZ - ZAPPA',
            'goz-pension': 'GOZ - Pension',
            'town-council': 'Town Council',
            'parastatal': 'Parastatal',
            'mission-private-schools': 'Mission and Private Schools',
            'entrepreneur': 'I am an Entrepreneur',
            'large-corporate': 'Large Corporate',
            'other': 'Other'
        };
        return employerMap[employerId] || data.employerName || employerId;
    };
    
    return (
        <div className="space-y-6">
            <div className="text-center">
                <CheckCircle className="mx-auto h-12 w-12 text-emerald-600 mb-4" />
                <h2 className="text-2xl font-semibold mb-2">Application Summary</h2>
                <p className="text-gray-600 dark:text-gray-400">
                    Review your application details before submitting
                </p>
            </div>
            
            <div className="grid gap-6 sm:grid-cols-2">
                <Card className="p-6">
                    <div className="flex items-center mb-4">
                        <User className="h-6 w-6 text-emerald-600 mr-3" />
                        <h3 className="text-lg font-semibold">Personal Information</h3>
                    </div>
                    <div className="space-y-3">
                        <div>
                            <p className="text-sm text-gray-500 dark:text-gray-400">Language</p>
                            <p className="font-medium">{data.language || 'English'}</p>
                        </div>
                        <div>
                            <p className="text-sm text-gray-500 dark:text-gray-400">Application Type</p>
                            <p className="font-medium">
                                {data.intent === 'hirePurchase' ? 'Hire Purchase Credit' : 
                                 data.intent === 'microBiz' ? 'Micro Biz Loan' : data.intent}
                            </p>
                        </div>
                    </div>
                </Card>

                <Card className="p-6">
                    <div className="flex items-center mb-4">
                        <Building className="h-6 w-6 text-emerald-600 mr-3" />
                        <h3 className="text-lg font-semibold">Employment</h3>
                    </div>
                    <div className="space-y-3">
                        <div>
                            <p className="text-sm text-gray-500 dark:text-gray-400">Employer</p>
                            <p className="font-medium">{getEmployerName(data.employer)}</p>
                        </div>
                        {data.specificEmployer && (
                            <div>
                                <p className="text-sm text-gray-500 dark:text-gray-400">Organization</p>
                                <p className="font-medium">{data.specificEmployer}</p>
                            </div>
                        )}
                    </div>
                </Card>

                {data.business && (
                    <Card className="p-6">
                        <div className="flex items-center mb-4">
                            <DollarSign className="h-6 w-6 text-emerald-600 mr-3" />
                            <h3 className="text-lg font-semibold">Business Details</h3>
                        </div>
                        <div className="space-y-3">
                            <div>
                                <p className="text-sm text-gray-500 dark:text-gray-400">Category</p>
                                <p className="font-medium">{data.category}</p>
                            </div>
                            <div>
                                <p className="text-sm text-gray-500 dark:text-gray-400">Business Type</p>
                                <p className="font-medium">{data.business}</p>
                            </div>
                            <div>
                                <p className="text-sm text-gray-500 dark:text-gray-400">Scale</p>
                                <p className="font-medium">{data.scale}</p>
                            </div>
                        </div>
                    </Card>
                )}

                {data.amount && (
                    <Card className="p-6">
                        <div className="flex items-center mb-4">
                            <CreditCard className="h-6 w-6 text-emerald-600 mr-3" />
                            <h3 className="text-lg font-semibold">Credit Terms</h3>
                        </div>
                        <div className="space-y-3">
                            <div>
                                <p className="text-sm text-gray-500 dark:text-gray-400">Loan Amount</p>
                                <p className="font-medium text-xl text-emerald-600">
                                    ${data.amount?.toLocaleString()}
                                </p>
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <p className="text-sm text-gray-500 dark:text-gray-400">Term</p>
                                    <p className="font-medium">{data.creditTerm} months</p>
                                </div>
                                <div>
                                    <p className="text-sm text-gray-500 dark:text-gray-400">Monthly Payment</p>
                                    <p className="font-medium">${data.monthlyPayment}</p>
                                </div>
                            </div>
                        </div>
                    </Card>
                )}

                <Card className="p-6">
                    <div className="flex items-center mb-4">
                        <Calendar className="h-6 w-6 text-emerald-600 mr-3" />
                        <h3 className="text-lg font-semibold">Account Information</h3>
                    </div>
                    <div className="space-y-3">
                        <div>
                            <p className="text-sm text-gray-500 dark:text-gray-400">Account Status</p>
                            <p className="font-medium">
                                {data.hasAccount ? `Has ${data.accountType}` : 
                                 data.wantsAccount ? `Will open ${data.accountType}` : 'No account'}
                            </p>
                        </div>
                        <div>
                            <p className="text-sm text-gray-500 dark:text-gray-400">Form Type</p>
                            <p className="font-medium text-sm text-emerald-600">{formId}</p>
                        </div>
                    </div>
                </Card>
            </div>
            
            <div className="flex justify-between pt-4">
                <Button
                    variant="outline"
                    onClick={onBack}
                    disabled={loading}
                    className="flex items-center gap-2"
                >
                    <ChevronLeft className="h-4 w-4" />
                    Back
                </Button>
                
                <Button
                    onClick={handleSubmit}
                    disabled={loading}
                    className="bg-emerald-600 hover:bg-emerald-700 px-8"
                >
                    {loading ? 'Loading...' : 'Continue to Form'}
                </Button>
            </div>
        </div>
    );
};

export default ApplicationSummary;