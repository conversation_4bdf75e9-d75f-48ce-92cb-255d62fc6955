<?php

namespace App\Services;

use App\Models\ApplicationState;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ReferenceCodeService
{
    /**
     * @var NotificationService
     */
    private $notificationService;
    
    /**
     * Constructor
     */
    public function __construct(NotificationService $notificationService = null)
    {
        $this->notificationService = $notificationService;
    }
    
    /**
     * Generate a unique 6-character alphanumeric reference code
     * 
     * @param string $sessionId The session ID to associate with the reference code
     * @return string The generated reference code
     * @throws \Exception If a unique code cannot be generated
     */
    public function generateReferenceCode(string $sessionId): string
    {
        // Try up to 5 times to generate a unique code
        $maxAttempts = 5;
        $attempt = 0;
        
        do {
            // Generate a 6-character alphanumeric code (uppercase for better readability)
            // Avoid characters that can be confused (0, O, 1, I, etc.)
            $code = strtoupper(Str::random(6, 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'));
            
            // Check if the code already exists
            $exists = $this->referenceCodeExists($code);
            
            $attempt++;
        } while ($exists && $attempt < $maxAttempts);
        
        // If we couldn't generate a unique code after max attempts, throw an exception
        if ($exists) {
            Log::error("Failed to generate a unique reference code after {$maxAttempts} attempts");
            throw new \Exception('Failed to generate a unique reference code after multiple attempts');
        }
        
        // Store the reference code with the application state
        $state = $this->storeReferenceCode($sessionId, $code);
        
        if (!$state) {
            Log::error("Failed to store reference code {$code} for session {$sessionId}");
            throw new \Exception("Failed to store reference code for session {$sessionId}");
        }
        
        // Send notification with the reference code
        if ($state && $this->notificationService) {
            $this->notificationService->sendReferenceCodeNotification($state, $code);
        }
        
        Log::info("Generated reference code {$code} for session {$sessionId}");
        return $code;
    }
    
    /**
     * Check if a reference code already exists
     * 
     * @param string $code The reference code to check
     * @return bool True if the code exists, false otherwise
     */
    public function referenceCodeExists(string $code): bool
    {
        return ApplicationState::where('reference_code', $code)->exists();
    }
    
    /**
     * Store a reference code with an application state
     * 
     * @param string $sessionId The session ID of the application state
     * @param string $code The reference code to store
     * @return ApplicationState|null The updated application state or null if not found
     */
    public function storeReferenceCode(string $sessionId, string $code): ?ApplicationState
    {
        $state = ApplicationState::where('session_id', $sessionId)->first();
        
        if ($state) {
            $state->update([
                'reference_code' => $code,
                'reference_code_expires_at' => Carbon::now()->addDays(30), // Reference codes valid for 30 days
            ]);
            
            return $state->fresh();
        }
        
        return null;
    }
    
    /**
     * Get an application state by reference code
     * 
     * @param string $code The reference code to look up
     * @return ApplicationState|null The application state or null if not found
     */
    public function getStateByReferenceCode(string $code): ?ApplicationState
    {
        return ApplicationState::where('reference_code', $code)
            ->where(function($query) {
                $query->whereNull('reference_code_expires_at')
                      ->orWhere('reference_code_expires_at', '>', Carbon::now());
            })
            ->first();
    }
    
    /**
     * Validate a reference code
     * 
     * @param string $code The reference code to validate
     * @return bool True if the code is valid, false otherwise
     */
    public function validateReferenceCode(string $code): bool
    {
        return $this->getStateByReferenceCode($code) !== null;
    }
    
    /**
     * Extend the expiration time of a reference code
     * 
     * @param string $code The reference code to extend
     * @param int $days The number of days to extend the expiration by
     * @return bool True if the code was extended, false otherwise
     */
    public function extendReferenceCode(string $code, int $days = 30): bool
    {
        $state = $this->getStateByReferenceCode($code);
        
        if ($state) {
            $state->update([
                'reference_code_expires_at' => Carbon::now()->addDays($days),
            ]);
            Log::info("Extended reference code {$code} expiration by {$days} days");
            return true;
        }
        
        return false;
    }
    
    /**
     * Get application status by reference code
     * 
     * @param string $code The reference code to look up
     * @return array|null The application status or null if not found
     */
    public function getApplicationStatusByReferenceCode(string $code): ?array
    {
        $state = $this->getStateByReferenceCode($code);
        
        if (!$state) {
            return null;
        }
        
        $metadata = $state->metadata ?? [];
        $status = $metadata['status'] ?? 'pending';
        
        return [
            'session_id' => $state->session_id,
            'status' => $status,
            'current_step' => $state->current_step,
            'created_at' => $state->created_at,
            'updated_at' => $state->updated_at,
        ];
    }
}