<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Controllers\PdfController;
use App\Http\Controllers\ApplicationWizardController;
use App\Http\Controllers\ApplicationPDFController;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::get('/download-ssb-form', [PdfController::class, 'downloadSsbForm'])->name('download.ssb.form');
Route::get('/download-zb-account-form', [PdfController::class, 'downloadZbAccountForm'])->name('download.zb.account.form');
Route::get('/download-account-holders-form', [PdfController::class, 'downloadAccountHoldersForm'])->name('download.account.holders.form');
Route::get('/download-sme-account-opening-form', [PdfController::class, 'downloadSmeAccountOpeningForm'])->name('download.sme.account.opening.form');

// Application Wizard Routes
Route::get('/application', [ApplicationWizardController::class, 'show'])->name('application.wizard');
Route::get('/application/resume/{identifier}', [ApplicationWizardController::class, 'resume'])->name('application.resume');
Route::get('/application/status', [ApplicationWizardController::class, 'status'])->name('application.status');
Route::get('/delivery/tracking', [ApplicationWizardController::class, 'tracking'])->name('delivery.tracking');
Route::get('/reference-code', [ApplicationWizardController::class, 'referenceCodeLookup'])->name('reference.code.lookup');

// Cross-platform synchronization routes
Route::post('/application/switch-to-whatsapp', [ApplicationWizardController::class, 'switchToWhatsApp'])->name('application.switch.whatsapp');
Route::post('/application/switch-to-web', [ApplicationWizardController::class, 'switchToWeb'])->name('application.switch.web');
Route::get('/application/sync-status', [ApplicationWizardController::class, 'getSyncStatus'])->name('application.sync.status');
Route::post('/application/synchronize', [ApplicationWizardController::class, 'synchronizeData'])->name('application.synchronize');

// PDF Routes
Route::get('/application/download/{sessionId}', [ApplicationPDFController::class, 'download'])->name('application.pdf.download');
Route::get('/application/view/{sessionId}', [ApplicationPDFController::class, 'view'])->name('application.pdf.view');
Route::post('/application/pdf/batch', [ApplicationPDFController::class, 'batchDownload'])->name('application.pdf.batch');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');
    
    // Admin routes
    Route::prefix('admin')->name('admin.')->group(function () {
        Route::get('/', [\App\Http\Controllers\AdminController::class, 'dashboard'])->name('dashboard');
    });
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
require __DIR__.'/test.php';
