<?php

namespace App\Http\Controllers;

use App\Models\ApplicationState;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Inertia\Inertia;
use Carbon\Carbon;

class AdminController extends Controller
{
    /**
     * Show admin dashboard
     */
    public function dashboard()
    {
        return Inertia::render('Admin/Dashboard');
    }

    /**
     * Get applications for admin dashboard
     */
    public function getApplications(): JsonResponse
    {
        $applications = ApplicationState::where('current_step', 'completed')
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($app) {
                $formData = $app->form_data ?? [];
                $formResponses = $formData['formResponses'] ?? [];
                $metadata = $app->metadata ?? [];
                
                // Get applicant name
                $applicantName = trim(
                    ($formResponses['firstName'] ?? '') . ' ' . 
                    ($formResponses['lastName'] ?? ($formResponses['surname'] ?? ''))
                ) ?: 'N/A';

                // Get business/product info
                $business = $formData['business'] ?? 'N/A';
                $loanAmount = $formData['amount'] ?? ($formResponses['loanAmount'] ?? '0');

                // Determine status
                $status = $this->determineApplicationStatus($app);

                return [
                    'id' => $app->id,
                    'sessionId' => $app->session_id,
                    'applicantName' => $applicantName,
                    'business' => $business,
                    'loanAmount' => $loanAmount,
                    'status' => $status,
                    'submittedAt' => $app->created_at->format('M j, Y g:i A'),
                    'channel' => $app->channel,
                ];
            });

        // Calculate stats
        $stats = [
            'total' => $applications->count(),
            'pending' => $applications->where('status', 'pending')->count(),
            'approved' => $applications->where('status', 'approved')->count(),
            'rejected' => $applications->where('status', 'rejected')->count(),
        ];

        return response()->json([
            'applications' => $applications->values(),
            'stats' => $stats,
        ]);
    }

    /**
     * Update application status
     */
    public function updateApplicationStatus(Request $request, string $sessionId): JsonResponse
    {
        $request->validate([
            'status' => 'required|in:pending,under_review,approved,rejected',
            'reason' => 'required_if:status,rejected',
            'approval_details' => 'nullable|array',
        ]);

        $application = ApplicationState::where('session_id', $sessionId)->first();
        
        if (!$application) {
            return response()->json(['error' => 'Application not found'], 404);
        }

        $metadata = $application->metadata ?? [];
        $metadata['status'] = $request->status;
        $metadata['status_updated_at'] = now()->toISOString();
        $metadata['status_updated_by'] = auth()->id() ?? 'admin';

        // Add status history
        $metadata['status_history'] = $metadata['status_history'] ?? [];
        $metadata['status_history'][] = [
            'status' => $request->status,
            'timestamp' => now()->toISOString(),
            'updated_by' => auth()->id() ?? 'admin',
            'reason' => $request->reason,
        ];

        if ($request->status === 'rejected') {
            $metadata['rejection_reason'] = $request->reason;
        }

        if ($request->status === 'approved' && $request->approval_details) {
            $metadata['approval_details'] = [
                'amount' => $request->approval_details['amount'] ?? $application->form_data['amount'] ?? 0,
                'approved_at' => now()->toISOString(),
                'disbursement_date' => $request->approval_details['disbursement_date'] ?? now()->addWeek()->toDateString(),
            ];
        }

        $application->metadata = $metadata;
        $application->save();

        return response()->json([
            'success' => true,
            'message' => 'Application status updated successfully',
            'status' => $request->status,
        ]);
    }

    /**
     * Determine the current status of the application
     */
    private function determineApplicationStatus(ApplicationState $application): string
    {
        $metadata = $application->metadata ?? [];
        
        // Check for explicit status in metadata
        if (isset($metadata['status'])) {
            return $metadata['status'];
        }

        // Determine based on application state
        if ($application->current_step === 'completed') {
            // Check if it's been reviewed
            if (isset($metadata['reviewed_at'])) {
                return isset($metadata['approved']) && $metadata['approved'] ? 'approved' : 'rejected';
            }
            return 'under_review';
        }

        return 'pending';
    }
}