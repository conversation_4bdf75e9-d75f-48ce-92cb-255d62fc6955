import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { ChevronLeft, User, Building, CreditCard, Users } from 'lucide-react';

interface SSBLoanFormProps {
    data: any;
    onNext: (data: any) => void;
    onBack: () => void;
    loading?: boolean;
}

const SSBLoanForm: React.FC<SSBLoanFormProps> = ({ data, onNext, onBack, loading }) => {
    // Calculate credit facility details from product selection
    const calculateCreditFacilityDetails = () => {
        const businessName = data.business; // string from ProductSelection
        const finalPrice = data.amount || 0; // number from ProductSelection
        const intent = data.intent || 'hirePurchase';
        
        let facilityType = '';
        if (intent === 'hirePurchase' && businessName) {
            facilityType = `Hire Purchase Credit - ${businessName}`;
        } else if ((intent === 'microBiz' || intent === 'microBizLoan') && businessName) {
            facilityType = `Micro Biz Loan - ${businessName}`;
        } else if (businessName) {
            // Fallback if intent doesn't match
            facilityType = `Credit Facility - ${businessName}`;
        }
        
        // Calculate tenure based on amount
        let tenure = 12; // default
        if (finalPrice <= 1000) tenure = 6;
        else if (finalPrice <= 5000) tenure = 12;
        else if (finalPrice <= 15000) tenure = 18;
        else tenure = 24;
        
        // Calculate monthly payment (10% annual interest)
        const interestRate = 0.10;
        const monthlyInterestRate = interestRate / 12;
        const monthlyPayment = finalPrice > 0 ? 
            (finalPrice * monthlyInterestRate * Math.pow(1 + monthlyInterestRate, tenure)) /
            (Math.pow(1 + monthlyInterestRate, tenure) - 1) : 0;
        
        return {
            creditFacilityType: facilityType,
            loanAmount: finalPrice.toFixed(2),
            loanTenure: tenure.toString(),
            monthlyPayment: monthlyPayment.toFixed(2),
            interestRate: '10.0'
        };
    };
    
    const creditDetails = calculateCreditFacilityDetails();
    const businessName = data.business;
    
    const [formData, setFormData] = useState({
        // Credit Facility Details (pre-populated)
        ...creditDetails,
        
        // Header Fields
        deliveryStatus: 'Future',
        province: '',
        agent: '',
        team: '',
        
        // Personal Details
        title: '',
        surname: '',
        firstName: '',
        gender: '',
        dateOfBirth: '',
        maritalStatus: '',
        nationality: 'Zimbabwean',
        idNumber: '',
        cellNumber: '',
        whatsApp: '',
        emailAddress: '',
        responsibleMinistry: '',
        employerName: '',
        employerAddress: '',
        permanentAddress: '',
        propertyOwnership: '',
        periodAtAddress: '',
        employmentStatus: '',
        jobTitle: '',
        dateOfEmployment: '',
        employmentNumber: '',
        headOfInstitution: '',
        headOfInstitutionCell: '',
        currentNetSalary: '',
        
        // Spouse and Next of Kin
        spouseDetails: [
            { fullName: '', relationship: '', phoneNumber: '', residentialAddress: '' },
            { fullName: '', relationship: '', phoneNumber: '', residentialAddress: '' },
            { fullName: '', relationship: '', phoneNumber: '', residentialAddress: '' }
        ],
        
        // Banking Details
        bankName: '',
        branch: '',
        accountNumber: '',
        
        // Other Loans
        otherLoans: [
            { institution: '', monthlyInstallment: '', currentBalance: '', maturityDate: '' },
            { institution: '', monthlyInstallment: '', currentBalance: '', maturityDate: '' }
        ],
        
        // Purpose/Asset (auto-populated from product selection)
        purposeAsset: businessName ? `${businessName} - ${data.scale || 'Standard Scale'}` : ''
    });

    const ministryOptions = [
        'Education',
        'Health',
        'Home Affairs',
        'Justice',
        'Other'
    ];

    const handleInputChange = (field: string, value: string) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleSpouseChange = (index: number, field: string, value: string) => {
        setFormData(prev => ({
            ...prev,
            spouseDetails: prev.spouseDetails.map((spouse, i) => 
                i === index ? { ...spouse, [field]: value } : spouse
            )
        }));
    };

    const handleLoanChange = (index: number, field: string, value: string) => {
        setFormData(prev => ({
            ...prev,
            otherLoans: prev.otherLoans.map((loan, i) => 
                i === index ? { ...loan, [field]: value } : loan
            )
        }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onNext(formData);
    };

    return (
        <div className="max-w-4xl mx-auto space-y-6">
            <div className="text-center">
                <h2 className="text-2xl font-semibold mb-2">SSB Loan Application Form</h2>
                <p className="text-gray-600 dark:text-gray-400">
                    Complete your SSB loan application details
                </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
                {/* Header Fields */}
                <Card className="p-6">
                    <div className="flex items-center mb-4">
                        <Building className="h-6 w-6 text-emerald-600 mr-3" />
                        <h3 className="text-lg font-semibold">Application Details</h3>
                    </div>
                    
                    <div className="grid gap-4 md:grid-cols-4">
                        <div>
                            <Label htmlFor="deliveryStatus">Delivery Status</Label>
                            <Select value={formData.deliveryStatus} onValueChange={(value) => handleInputChange('deliveryStatus', value)}>
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="Future">Future</SelectItem>
                                    <SelectItem value="Present">Present</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        
                        <div>
                            <Label htmlFor="province">Province</Label>
                            <Input
                                id="province"
                                value={formData.province}
                                onChange={(e) => handleInputChange('province', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="agent">Agent</Label>
                            <Input
                                id="agent"
                                value={formData.agent}
                                onChange={(e) => handleInputChange('agent', e.target.value)}
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="team">Team</Label>
                            <Input
                                id="team"
                                value={formData.team}
                                onChange={(e) => handleInputChange('team', e.target.value)}
                            />
                        </div>
                    </div>
                </Card>

                {/* Personal Details */}
                <Card className="p-6">
                    <div className="flex items-center mb-4">
                        <User className="h-6 w-6 text-emerald-600 mr-3" />
                        <h3 className="text-lg font-semibold">Personal Details</h3>
                    </div>
                    
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        <div>
                            <Label htmlFor="title">Title</Label>
                            <Select value={formData.title} onValueChange={(value) => handleInputChange('title', value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Select title" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="Mr">Mr</SelectItem>
                                    <SelectItem value="Mrs">Mrs</SelectItem>
                                    <SelectItem value="Miss">Miss</SelectItem>
                                    <SelectItem value="Dr">Dr</SelectItem>
                                    <SelectItem value="Prof">Prof</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        
                        <div>
                            <Label htmlFor="surname">Surname</Label>
                            <Input
                                id="surname"
                                value={formData.surname}
                                onChange={(e) => handleInputChange('surname', e.target.value)}
                                required
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="firstName">First Name</Label>
                            <Input
                                id="firstName"
                                value={formData.firstName}
                                onChange={(e) => handleInputChange('firstName', e.target.value)}
                                required
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="gender">Gender</Label>
                            <Select value={formData.gender} onValueChange={(value) => handleInputChange('gender', value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Select gender" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="Male">Male</SelectItem>
                                    <SelectItem value="Female">Female</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        
                        <div>
                            <Label htmlFor="dateOfBirth">Date of Birth</Label>
                            <Input
                                id="dateOfBirth"
                                type="date"
                                value={formData.dateOfBirth}
                                onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                                required
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="idNumber">ID Number</Label>
                            <Input
                                id="idNumber"
                                value={formData.idNumber}
                                onChange={(e) => handleInputChange('idNumber', e.target.value)}
                                required
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="cellNumber">Cell Number</Label>
                            <Input
                                id="cellNumber"
                                value={formData.cellNumber}
                                onChange={(e) => handleInputChange('cellNumber', e.target.value)}
                                required
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="emailAddress">Email Address</Label>
                            <Input
                                id="emailAddress"
                                type="email"
                                value={formData.emailAddress}
                                onChange={(e) => handleInputChange('emailAddress', e.target.value)}
                                required
                            />
                        </div>
                        
                        <div className="md:col-span-2 lg:col-span-3">
                            <Label>Name of Responsible Ministry</Label>
                            <div className="flex gap-4 mt-2">
                                {['Education', 'Health', 'Home Affairs', 'Justice', 'Other'].map((ministry) => (
                                    <label key={ministry} className="flex items-center space-x-2">
                                        <input
                                            type="radio"
                                            name="responsibleMinistry"
                                            value={ministry}
                                            checked={formData.responsibleMinistry === ministry}
                                            onChange={(e) => handleInputChange('responsibleMinistry', e.target.value)}
                                            className="text-emerald-600"
                                        />
                                        <span className="text-sm">{ministry}</span>
                                    </label>
                                ))}
                            </div>
                        </div>
                    </div>
                </Card>

                {/* Employment Details */}
                <Card className="p-6">
                    <div className="flex items-center mb-4">
                        <Building className="h-6 w-6 text-emerald-600 mr-3" />
                        <h3 className="text-lg font-semibold">Employment Details</h3>
                    </div>
                    
                    <div className="grid gap-4 md:grid-cols-2">
                        <div>
                            <Label htmlFor="employerName">Name of Employer</Label>
                            <Input
                                id="employerName"
                                value={formData.employerName}
                                onChange={(e) => handleInputChange('employerName', e.target.value)}
                                required
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="employerAddress">Employer Address</Label>
                            <Input
                                id="employerAddress"
                                value={formData.employerAddress}
                                onChange={(e) => handleInputChange('employerAddress', e.target.value)}
                                required
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="jobTitle">Job Title</Label>
                            <Input
                                id="jobTitle"
                                value={formData.jobTitle}
                                onChange={(e) => handleInputChange('jobTitle', e.target.value)}
                                required
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="employmentNumber">Employment Number</Label>
                            <Input
                                id="employmentNumber"
                                value={formData.employmentNumber}
                                onChange={(e) => handleInputChange('employmentNumber', e.target.value)}
                                required
                            />
                        </div>
                        
                        <div>
                            <Label htmlFor="currentNetSalary">Current Net Salary (USD)</Label>
                            <Input
                                id="currentNetSalary"
                                type="number"
                                value={formData.currentNetSalary}
                                onChange={(e) => handleInputChange('currentNetSalary', e.target.value)}
                                required
                            />
                        </div>
                    </div>
                </Card>

                {/* Spouse and Next of Kin */}
                <Card className="p-6">
                    <div className="flex items-center mb-4">
                        <Users className="h-6 w-6 text-emerald-600 mr-3" />
                        <h3 className="text-lg font-semibold">Spouse and Next of Kin Details</h3>
                    </div>
                    
                    {formData.spouseDetails.map((spouse, index) => (
                        <div key={index} className="grid gap-4 md:grid-cols-4 mb-4 p-4 border rounded-lg">
                            <div>
                                <Label htmlFor={`spouse-${index}-name`}>Full Name</Label>
                                <Input
                                    id={`spouse-${index}-name`}
                                    value={spouse.fullName}
                                    onChange={(e) => handleSpouseChange(index, 'fullName', e.target.value)}
                                />
                            </div>
                            
                            <div>
                                <Label htmlFor={`spouse-${index}-relationship`}>Relationship</Label>
                                <Input
                                    id={`spouse-${index}-relationship`}
                                    value={spouse.relationship}
                                    onChange={(e) => handleSpouseChange(index, 'relationship', e.target.value)}
                                />
                            </div>
                            
                            <div>
                                <Label htmlFor={`spouse-${index}-phone`}>Phone Number</Label>
                                <Input
                                    id={`spouse-${index}-phone`}
                                    value={spouse.phoneNumber}
                                    onChange={(e) => handleSpouseChange(index, 'phoneNumber', e.target.value)}
                                />
                            </div>
                            
                            <div>
                                <Label htmlFor={`spouse-${index}-address`}>Residential Address</Label>
                                <Input
                                    id={`spouse-${index}-address`}
                                    value={spouse.residentialAddress}
                                    onChange={(e) => handleSpouseChange(index, 'residentialAddress', e.target.value)}
                                />
                            </div>
                        </div>
                    ))}
                </Card>

                {/* Credit Facility Details */}
                <Card className="p-6 bg-green-50 dark:bg-green-900 border-green-200 dark:border-green-700">
                    <div className="flex items-center mb-4">
                        <CreditCard className="h-6 w-6 text-green-600 mr-3" />
                        <h3 className="text-lg font-semibold text-green-800 dark:text-green-200">Credit Facility Application Details</h3>
                    </div>
                    
                    {/* Pre-populated readonly fields */}
                    <div className="grid gap-4 mb-6 p-4 bg-white dark:bg-gray-800 rounded-lg border">
                        <div className="text-sm text-green-600 dark:text-green-400 font-medium mb-2">
                            ✅ The following details have been automatically filled based on your product selection:
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <Label className="text-gray-700 dark:text-gray-300">Credit Facility Type</Label>
                                <Input
                                    value={formData.creditFacilityType}
                                    readOnly
                                    className="bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600"
                                />
                            </div>
                            <div>
                                <Label className="text-gray-700 dark:text-gray-300">Price/Applied Amount (USD)</Label>
                                <div className="relative">
                                    <span className="absolute left-3 top-2.5 text-gray-500">$</span>
                                    <Input
                                        value={formData.loanAmount}
                                        readOnly
                                        className="pl-8 bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600"
                                    />
                                </div>
                            </div>
                            <div>
                                <Label className="text-gray-700 dark:text-gray-300">Loan Tenure (Months)</Label>
                                <Input
                                    value={`${formData.loanTenure} months`}
                                    readOnly
                                    className="bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600"
                                />
                            </div>
                            <div>
                                <Label className="text-gray-700 dark:text-gray-300">Monthly Payment (USD)</Label>
                                <div className="relative">
                                    <span className="absolute left-3 top-2.5 text-gray-500">$</span>
                                    <Input
                                        value={formData.monthlyPayment}
                                        readOnly
                                        className="pl-8 bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600"
                                    />
                                </div>
                            </div>
                            <div>
                                <Label className="text-gray-700 dark:text-gray-300">Interest Rate (%)</Label>
                                <Input
                                    value={`${formData.interestRate}%`}
                                    readOnly
                                    className="bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600"
                                />
                            </div>
                        </div>
                    </div>
                    
                    {/* Editable purpose field */}
                    <div>
                        <Label htmlFor="purposeAsset">Purpose/Asset Applied For</Label>
                        <Textarea
                            id="purposeAsset"
                            value={formData.purposeAsset}
                            onChange={(e) => handleInputChange('purposeAsset', e.target.value)}
                            rows={4}
                            required
                            placeholder="Describe the purpose and asset details..."
                        />
                    </div>
                </Card>

                <div className="flex justify-between pt-4">
                    <Button
                        type="button"
                        variant="outline"
                        onClick={onBack}
                        disabled={loading}
                        className="flex items-center gap-2"
                    >
                        <ChevronLeft className="h-4 w-4" />
                        Back
                    </Button>
                    
                    <Button
                        type="submit"
                        disabled={loading}
                        className="bg-emerald-600 hover:bg-emerald-700 px-8"
                    >
                        {loading ? 'Submitting...' : 'Submit Application'}
                    </Button>
                </div>
            </form>
        </div>
    );
};

export default SSBLoanForm;