import axios from 'axios';

// Types matching the existing frontend structure
export interface BusinessType {
  id?: number;
  name: string;
  basePrice: number;
  scales: {
    id?: number;
    name: string;
    multiplier: number;
  }[];
  tenure?: number;
}

export interface Subcategory {
  name: string;
  businesses: BusinessType[];
}

export interface Category {
  id: string;
  name: string;
  emoji: string;
  subcategories: Subcategory[];
}

export interface CreditTermOption {
  months: number;
  monthlyPayment: number;
}

class ProductService {
  private baseUrl = '/api/products';
  private cache: Category[] | null = null;
  private cacheTimestamp: number | null = null;
  private cacheExpiry = 5 * 60 * 1000; // 5 minutes

  /**
   * Get all product categories with caching
   */
  async getProductCategories(): Promise<Category[]> {
    // Check cache first
    if (this.cache && this.cacheTimestamp && 
        (Date.now() - this.cacheTimestamp) < this.cacheExpiry) {
      return this.cache;
    }

    try {
      const response = await axios.get(`${this.baseUrl}/frontend-catalog`);
      this.cache = response.data;
      this.cacheTimestamp = Date.now();
      return this.cache;
    } catch (error) {
      console.error('Failed to fetch product categories:', error);
      
      // Return fallback empty structure if API fails
      return this.getFallbackCategories();
    }
  }

  /**
   * Search products by name or category
   */
  async searchProducts(query: string, categoryId?: string): Promise<BusinessType[]> {
    try {
      const params = new URLSearchParams({ query });
      if (categoryId) {
        params.append('category_id', categoryId);
      }

      const response = await axios.get(`${this.baseUrl}/search?${params}`);
      
      if (response.data.success) {
        return response.data.data.map((product: any) => ({
          id: product.id,
          name: product.name,
          basePrice: product.base_price,
          scales: product.package_sizes.map((size: any) => ({
            id: size.id,
            name: size.name,
            multiplier: size.multiplier,
          })),
          tenure: 24,
        }));
      }
      
      return [];
    } catch (error) {
      console.error('Failed to search products:', error);
      return [];
    }
  }

  /**
   * Get a specific product by ID
   */
  async getProduct(productId: number): Promise<BusinessType | null> {
    try {
      const response = await axios.get(`${this.baseUrl}/product/${productId}`);
      
      if (response.data.success) {
        const product = response.data.data;
        return {
          id: product.id,
          name: product.name,
          basePrice: product.base_price,
          scales: product.package_sizes.map((size: any) => ({
            id: size.id,
            name: size.name,
            multiplier: size.multiplier,
          })),
          tenure: 24,
        };
      }
      
      return null;
    } catch (error) {
      console.error('Failed to fetch product:', error);
      return null;
    }
  }

  /**
   * Get products by category
   */
  async getProductsByCategory(categoryId: number): Promise<BusinessType[]> {
    try {
      const response = await axios.get(`${this.baseUrl}/category/${categoryId}`);
      
      if (response.data.success) {
        return response.data.data.map((product: any) => ({
          id: product.id,
          name: product.name,
          basePrice: product.base_price,
          scales: product.package_sizes.map((size: any) => ({
            id: size.id,
            name: size.name,
            multiplier: size.multiplier,
          })),
          tenure: 24,
        }));
      }
      
      return [];
    } catch (error) {
      console.error('Failed to fetch products by category:', error);
      return [];
    }
  }

  /**
   * Calculate credit term options
   */
  getCreditTermOptions(amount: number): CreditTermOption[] {
    const terms = [6, 12, 18, 24, 36];
    return terms.map(months => ({
      months,
      monthlyPayment: Math.round(amount / months * 1.1) // 10% interest approximation
    }));
  }

  /**
   * Clear cache (useful for admin updates)
   */
  clearCache(): void {
    this.cache = null;
    this.cacheTimestamp = null;
  }

  /**
   * Fallback categories if API fails
   */
  private getFallbackCategories(): Category[] {
    return [
      {
        id: 'agriculture',
        name: 'Agriculture',
        emoji: '🌾',
        subcategories: [
          {
            name: 'Cash Crops',
            businesses: [
              {
                name: 'Cotton',
                basePrice: 800,
                scales: [
                  { name: '1 Ha', multiplier: 1 },
                  { name: '2 Ha', multiplier: 2 },
                  { name: '3 Ha', multiplier: 3 },
                  { name: '5 Ha', multiplier: 5 }
                ]
              }
            ]
          }
        ]
      },
      {
        id: 'catering',
        name: 'Catering',
        emoji: '🍽️',
        subcategories: [
          {
            name: 'Food Services',
            businesses: [
              {
                name: 'Baking – Bread',
                basePrice: 1000,
                scales: [
                  { name: 'Small', multiplier: 1 },
                  { name: 'Medium', multiplier: 2 },
                  { name: 'Large', multiplier: 3 }
                ]
              }
            ]
          }
        ]
      }
    ];
  }

  /**
   * Get statistics about the product catalog
   */
  async getStatistics() {
    try {
      const response = await axios.get(`${this.baseUrl}/statistics`);
      return response.data;
    } catch (error) {
      console.error('Failed to fetch product statistics:', error);
      return null;
    }
  }
}

// Export singleton instance
export const productService = new ProductService();

// Export the getCreditTermOptions function for backward compatibility
export const getCreditTermOptions = (amount: number) => {
  return productService.getCreditTermOptions(amount);
};
