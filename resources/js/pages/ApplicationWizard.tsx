import { Head } from '@inertiajs/react';
import ApplicationWizard from '@/components/ApplicationWizard/ApplicationWizard';
import ErrorBoundary from '@/components/ErrorBoundary';

interface ApplicationWizardPageProps {
    initialStep?: string;
    initialData?: any;
    sessionId?: string;
}

export default function ApplicationWizardPage({ 
    initialStep = 'language', 
    initialData = {},
    sessionId
}: ApplicationWizardPageProps) {
    return (
        <>
            <Head title="Application Wizard" />
            <ErrorBoundary>
                <ApplicationWizard
                    initialStep={initialStep}
                    initialData={initialData}
                    sessionId={sessionId}
                />
            </ErrorBoundary>
        </>
    );
}