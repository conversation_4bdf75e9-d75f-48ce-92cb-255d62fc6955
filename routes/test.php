<?php

use Illuminate\Support\Facades\Route;
use App\Models\ApplicationState;
use App\Services\PDFGeneratorService;

// Test PDF generation route
Route::get('/test-pdf/{sessionId?}', function ($sessionId = null) {
    try {
        // Use test session ID if none provided
        $sessionId = $sessionId ?: 'test_1752244141';
        
        $state = ApplicationState::where('session_id', $sessionId)->first();
        
        if (!$state) {
            return response()->json([
                'error' => 'Application not found',
                'session_id' => $sessionId,
                'available' => ApplicationState::pluck('session_id')->take(5)
            ], 404);
        }
        
        $pdfGenerator = new PDFGeneratorService();
        $pdfPath = $pdfGenerator->generateApplicationPDF($state);
        
        return response()->json([
            'success' => true,
            'session_id' => $sessionId,
            'pdf_path' => $pdfPath,
            'download_url' => url("/application/download/{$sessionId}"),
            'view_url' => url("/application/view/{$sessionId}"),
            'file_exists' => \Storage::disk('public')->exists($pdfPath),
            'file_size' => \Storage::disk('public')->exists($pdfPath) ? \Storage::disk('public')->size($pdfPath) : 0,
        ]);
        
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
});