<?php

namespace App\Http\Controllers;

use App\Models\ApplicationState;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;

class DeliveryTrackingController extends Controller
{
    /**
     * Get delivery tracking information by reference number
     */
    public function getTracking(string $reference): JsonResponse
    {
        // Look up application by session ID or resume code
        $application = ApplicationState::where('session_id', $reference)
            ->orWhere(function ($query) use ($reference) {
                if (strlen($reference) === 6 && ctype_alnum($reference)) {
                    $query->whereJsonContains('metadata->resume_code', strtoupper($reference));
                }
            })
            ->first();

        if (!$application) {
            return response()->json(['error' => 'Delivery record not found'], 404);
        }

        // Check if this application has been approved (prerequisite for delivery)
        $metadata = $application->metadata ?? [];
        $applicationStatus = $metadata['status'] ?? 'pending';
        
        if (!in_array($applicationStatus, ['approved', 'completed'])) {
            return response()->json([
                'error' => 'No delivery found. Application must be approved first.'
            ], 404);
        }

        $formData = $application->form_data ?? [];
        $formResponses = $formData['formResponses'] ?? [];
        
        // Get delivery status from metadata or generate mock data
        $deliveryStatus = $this->getDeliveryStatus($metadata);
        
        // Build timeline
        $timeline = $this->buildDeliveryTimeline($application, $deliveryStatus);
        
        // Get recipient info
        $recipientName = trim(
            ($formResponses['firstName'] ?? '') . ' ' . 
            ($formResponses['lastName'] ?? ($formResponses['surname'] ?? ''))
        ) ?: 'N/A';

        // Get product info
        $product = $this->formatProductInfo($formData);
        
        // Generate tracking number if not exists
        $trackingNumber = $metadata['tracking_number'] ?? $this->generateTrackingNumber($application->id);

        // Format response
        $response = [
            'sessionId' => $application->session_id,
            'status' => $deliveryStatus,
            'product' => $product,
            'recipientName' => $recipientName,
            'deliveryAddress' => $formResponses['address'] ?? $formResponses['residentialAddress'] ?? 'N/A',
            'trackingNumber' => $trackingNumber,
            'estimatedDelivery' => $this->getEstimatedDelivery($metadata),
            'timeline' => $timeline,
        ];

        // Add current location for in-transit deliveries
        if (in_array($deliveryStatus, ['in_transit', 'out_for_delivery'])) {
            $response['currentLocation'] = $metadata['current_location'] ?? 'Harare Distribution Center';
        }

        // Add driver info for out_for_delivery status
        if ($deliveryStatus === 'out_for_delivery') {
            $response['driverName'] = $metadata['driver_name'] ?? 'John Mukamuri';
            $response['driverPhone'] = $metadata['driver_phone'] ?? '+263 77 123 4567';
        }

        // Add actual delivery date if delivered
        if ($deliveryStatus === 'delivered') {
            $response['actualDelivery'] = isset($metadata['delivered_at']) 
                ? Carbon::parse($metadata['delivered_at'])->format('F j, Y g:i A')
                : Carbon::now()->subDays(rand(1, 3))->format('F j, Y g:i A');
        }

        // Add delivery notes
        if (isset($metadata['delivery_notes'])) {
            $response['deliveryNotes'] = $metadata['delivery_notes'];
        }

        return response()->json($response);
    }

    /**
     * Update delivery tracking information (for admin use)
     */
    public function updateTracking(Request $request, string $sessionId): JsonResponse
    {
        $request->validate([
            'status' => 'required|in:processing,dispatched,in_transit,out_for_delivery,delivered,failed',
            'location' => 'nullable|string',
            'driver_name' => 'nullable|string',
            'driver_phone' => 'nullable|string',
            'notes' => 'nullable|string',
            'estimated_delivery' => 'nullable|date',
        ]);

        $application = ApplicationState::where('session_id', $sessionId)->first();
        
        if (!$application) {
            return response()->json(['error' => 'Application not found'], 404);
        }

        $metadata = $application->metadata ?? [];
        $metadata['delivery_status'] = $request->status;
        $metadata['delivery_updated_at'] = now()->toIso8601String();
        
        // Add tracking history
        $metadata['delivery_history'] = $metadata['delivery_history'] ?? [];
        $metadata['delivery_history'][] = [
            'status' => $request->status,
            'location' => $request->location,
            'timestamp' => now()->toIso8601String(),
            'notes' => $request->notes,
        ];

        // Update specific fields based on status
        if ($request->location) {
            $metadata['current_location'] = $request->location;
        }

        if ($request->driver_name) {
            $metadata['driver_name'] = $request->driver_name;
        }

        if ($request->driver_phone) {
            $metadata['driver_phone'] = $request->driver_phone;
        }

        if ($request->notes) {
            $metadata['delivery_notes'] = $request->notes;
        }

        if ($request->estimated_delivery) {
            $metadata['estimated_delivery'] = $request->estimated_delivery;
        }

        if ($request->status === 'delivered') {
            $metadata['delivered_at'] = now()->toIso8601String();
        }

        $application->metadata = $metadata;
        $application->save();

        return response()->json([
            'message' => 'Delivery status updated successfully',
            'status' => $request->status,
        ]);
    }

    /**
     * Get delivery status from metadata or determine based on application
     */
    private function getDeliveryStatus(array $metadata): string
    {
        // Check for explicit delivery status
        if (isset($metadata['delivery_status'])) {
            return $metadata['delivery_status'];
        }

        // Determine based on application approval date
        if (isset($metadata['approval_details']['approved_at'])) {
            $approvedAt = Carbon::parse($metadata['approval_details']['approved_at']);
            $daysSinceApproval = $approvedAt->diffInDays(now());

            if ($daysSinceApproval >= 7) {
                return 'delivered';
            } elseif ($daysSinceApproval >= 5) {
                return 'out_for_delivery';
            } elseif ($daysSinceApproval >= 3) {
                return 'in_transit';
            } elseif ($daysSinceApproval >= 1) {
                return 'dispatched';
            }
        }

        return 'processing';
    }

    /**
     * Build delivery timeline
     */
    private function buildDeliveryTimeline(ApplicationState $application, string $currentStatus): array
    {
        $timeline = [];
        $metadata = $application->metadata ?? [];
        
        // Order Processing
        $timeline[] = [
            'id' => 1,
            'title' => 'Order Processing',
            'location' => 'ZB Bank Processing Center',
            'timestamp' => isset($metadata['approval_details']['approved_at'])
                ? Carbon::parse($metadata['approval_details']['approved_at'])->format('F j, Y g:i A')
                : $application->updated_at->format('F j, Y g:i A'),
            'status' => 'completed',
        ];

        // Dispatched
        if (in_array($currentStatus, ['dispatched', 'in_transit', 'out_for_delivery', 'delivered'])) {
            $dispatchTime = isset($metadata['dispatched_at'])
                ? Carbon::parse($metadata['dispatched_at'])
                : $this->estimateDispatchTime($metadata);
                
            $timeline[] = [
                'id' => 2,
                'title' => 'Dispatched from Warehouse',
                'location' => 'Harare Central Warehouse',
                'timestamp' => $dispatchTime->format('F j, Y g:i A'),
                'status' => 'completed',
            ];
        } else {
            $timeline[] = [
                'id' => 2,
                'title' => 'Dispatch from Warehouse',
                'location' => 'Pending dispatch',
                'timestamp' => 'Pending',
                'status' => 'pending',
            ];
        }

        // In Transit
        if (in_array($currentStatus, ['in_transit', 'out_for_delivery', 'delivered'])) {
            $transitTime = isset($metadata['in_transit_at'])
                ? Carbon::parse($metadata['in_transit_at'])
                : $this->estimateTransitTime($metadata);
                
            $timeline[] = [
                'id' => 3,
                'title' => 'In Transit',
                'location' => $metadata['current_location'] ?? 'En route to delivery location',
                'timestamp' => $transitTime->format('F j, Y g:i A'),
                'status' => $currentStatus === 'in_transit' ? 'current' : 'completed',
            ];
        } else {
            $timeline[] = [
                'id' => 3,
                'title' => 'In Transit',
                'location' => 'Pending',
                'timestamp' => 'Pending',
                'status' => 'pending',
            ];
        }

        // Out for Delivery
        if (in_array($currentStatus, ['out_for_delivery', 'delivered'])) {
            $outForDeliveryTime = isset($metadata['out_for_delivery_at'])
                ? Carbon::parse($metadata['out_for_delivery_at'])
                : $this->estimateOutForDeliveryTime($metadata);
                
            $timeline[] = [
                'id' => 4,
                'title' => 'Out for Delivery',
                'location' => 'Local delivery vehicle',
                'timestamp' => $outForDeliveryTime->format('F j, Y g:i A'),
                'status' => $currentStatus === 'out_for_delivery' ? 'current' : 'completed',
            ];
        } else {
            $timeline[] = [
                'id' => 4,
                'title' => 'Out for Delivery',
                'location' => 'Pending',
                'timestamp' => 'Pending',
                'status' => 'pending',
            ];
        }

        // Delivered
        if ($currentStatus === 'delivered') {
            $deliveredTime = isset($metadata['delivered_at'])
                ? Carbon::parse($metadata['delivered_at'])
                : now();
                
            $timeline[] = [
                'id' => 5,
                'title' => 'Delivered',
                'location' => 'Delivery address',
                'timestamp' => $deliveredTime->format('F j, Y g:i A'),
                'status' => 'completed',
            ];
        } else {
            $timeline[] = [
                'id' => 5,
                'title' => 'Delivery',
                'location' => 'Delivery address',
                'timestamp' => 'Pending',
                'status' => 'pending',
            ];
        }

        return $timeline;
    }

    /**
     * Format product information
     */
    private function formatProductInfo(array $formData): string
    {
        $business = $formData['business'] ?? 'Unknown Product';
        $scale = $formData['scale'] ?? '';
        
        if ($scale) {
            return "{$business} - {$scale}";
        }
        
        return $business;
    }

    /**
     * Generate tracking number
     */
    private function generateTrackingNumber(int $applicationId): string
    {
        return 'TRK' . now()->format('Y') . str_pad($applicationId, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Get estimated delivery date
     */
    private function getEstimatedDelivery(array $metadata): string
    {
        if (isset($metadata['estimated_delivery'])) {
            return Carbon::parse($metadata['estimated_delivery'])->format('F j, Y');
        }

        // Default to 5-7 business days from approval
        if (isset($metadata['approval_details']['approved_at'])) {
            return Carbon::parse($metadata['approval_details']['approved_at'])
                ->addBusinessDays(7)
                ->format('F j, Y');
        }

        return now()->addBusinessDays(7)->format('F j, Y');
    }

    /**
     * Estimate dispatch time
     */
    private function estimateDispatchTime(array $metadata): Carbon
    {
        if (isset($metadata['approval_details']['approved_at'])) {
            return Carbon::parse($metadata['approval_details']['approved_at'])->addBusinessDay();
        }
        
        return now()->subDays(2);
    }

    /**
     * Estimate transit time
     */
    private function estimateTransitTime(array $metadata): Carbon
    {
        return $this->estimateDispatchTime($metadata)->addBusinessDay();
    }

    /**
     * Estimate out for delivery time
     */
    private function estimateOutForDeliveryTime(array $metadata): Carbon
    {
        return $this->estimateTransitTime($metadata)->addBusinessDays(2);
    }
}